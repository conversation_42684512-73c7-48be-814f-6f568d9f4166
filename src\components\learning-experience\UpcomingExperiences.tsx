
import { useState } from "react";
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight, Calendar, Clock, MapPin, Check, CreditCard } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

// Upcoming experiences data
const upcomingExperiences = [
  {
    id: 1,
    title: "Vale do Silício",
    date: "17 a 22 de Outubro, 2023",
    duration: "6 dias",
    location: "San Francisco, EUA",
    price: "R$ 18.900",
    description: "Visite as maiores empresas de tecnologia do mundo e conheça de perto o ecossistema que transformou a maneira como vivemos",
    image: "https://images.unsplash.com/photo-1521747116042-5a810fda9664?q=80&w=2940&auto=format&fit=crop",
    highlights: [
      "Visitas às sedes do Google, Apple e Meta",
      "Workshop exclusivo com empreendedores locais",
      "Reuniões com investidores do Vale"
    ]
  },
  {
    id: 2,
    title: "AI Learning Experience",
    date: "5 a 10 de Novembro, 2023",
    duration: "5 dias",
    location: "San Francisco, EUA",
    price: "R$ 17.500",
    description: "Mergulhe no universo da Inteligência Artificial e conheça as empresas que estão remodelando o futuro com IA",
    image: "https://images.unsplash.com/photo-1557838429-06783d16b871?q=80&w=2940&auto=format&fit=crop",
    highlights: [
      "Visita ao centro de pesquisa de IA da OpenAI",
      "Encontro com fundadores de startups de IA",
      "Workshop prático de implementação de IA"
    ]
  },
  {
    id: 3,
    title: "Imersão China Fintech",
    date: "12 a 19 de Janeiro, 2024",
    duration: "7 dias",
    location: "Xangai e Pequim, China",
    price: "R$ 22.900",
    description: "Conheça o ecossistema fintech mais avançado do mundo e descubra como a China revolucionou os sistemas de pagamento",
    image: "https://images.unsplash.com/photo-1461695008884-244cb4543d74?q=80&w=2940&auto=format&fit=crop",
    highlights: [
      "Visitas às sedes da Ant Group e Tencent",
      "Demonstrações práticas de sistemas de pagamento",
      "Conexões com aceleradoras locais"
    ]
  }
];

const UpcomingExperiences = () => {
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const [selectedExperience, setSelectedExperience] = useState(null);
  const { toast } = useToast();

  const handleRequestInfo = (experience) => {
    setSelectedExperience(experience);
    setShowPaymentDialog(true);
  };

  const handlePaymentCompletion = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Pagamento concluído!",
      description: `Inscrição confirmada para ${selectedExperience?.title}. Você receberá mais informações por email.`,
      variant: "default",
    });
  };

  return (
    <section id="upcoming" className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Próximas <span className="text-brand">Imersões</span>
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Escolha seu destino e prepare-se para uma experiência transformadora
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {upcomingExperiences.map((experience) => (
            <div key={experience.id} className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 flex flex-col">
              <div className="relative h-56 overflow-hidden">
                <img 
                  src={experience.image} 
                  alt={experience.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
                  <h3 className="font-montserrat text-xl font-semibold text-white">
                    {experience.title}
                  </h3>
                </div>
              </div>
              
              <div className="p-6 flex-grow">
                <p className="text-gray-700 mb-4">{experience.description}</p>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center">
                    <Calendar className="h-5 w-5 text-brand-accent mr-3" />
                    <span>{experience.date}</span>
                  </div>
                  <div className="flex items-center">
                    <Clock className="h-5 w-5 text-brand mr-3" />
                    <span>{experience.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-red-500 mr-3" />
                    <span>{experience.location}</span>
                  </div>
                </div>
                
                <div className="mt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Destaques:</h4>
                  <ul className="space-y-1">
                    {experience.highlights.map((highlight, index) => (
                      <li key={index} className="flex items-start">
                        <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                        <span className="text-gray-700">{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
              
              <div className="p-6 pt-0 border-t border-gray-100 mt-4">
                <div className="flex items-center justify-between mb-4">
                  <span className="text-gray-500">Investimento a partir de:</span>
                  <span className="text-brand font-bold text-xl">{experience.price}</span>
                </div>
                <Button 
                  className="w-full bg-brand-accent hover:bg-brand-accent/90 text-white"
                  onClick={() => handleRequestInfo(experience)}
                >
                  Solicitar Informações
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Finalizar Inscrição</DialogTitle>
            <DialogDescription>
              Complete o pagamento para garantir sua vaga na experiência {selectedExperience?.title}.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">{selectedExperience?.title}</h3>
              <p className="text-sm text-gray-500 mb-2">{selectedExperience?.date} • {selectedExperience?.location}</p>
              <div className="flex justify-between items-center">
                <span className="font-semibold">{selectedExperience?.price}</span>
                <span className="text-xs text-green-600">Entrada + 10x</span>
              </div>
            </div>
            <div className="grid gap-2">
              <label htmlFor="cardNumber" className="text-sm font-medium">Número do Cartão</label>
              <input 
                id="cardNumber" 
                type="text" 
                placeholder="0000 0000 0000 0000"
                className="border rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="expiry" className="text-sm font-medium">Validade</label>
                <input 
                  id="expiry" 
                  type="text" 
                  placeholder="MM/AA"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="cvc" className="text-sm font-medium">CVC</label>
                <input 
                  id="cvc" 
                  type="text" 
                  placeholder="123"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>Cancelar</Button>
            <Button className="bg-brand hover:bg-brand-secondary" onClick={handlePaymentCompletion}>
              <CreditCard className="mr-2 h-4 w-4" />
              Finalizar Pagamento
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </section>
  );
};

export default UpcomingExperiences;
