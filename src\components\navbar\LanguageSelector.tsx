
import { ChevronDown } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";

type Language = "pt-BR" | "en" | "es";

interface LanguageSelectorProps {
  currentLanguage: Language;
  onLanguageChange: (language: Language) => void;
}

export const LanguageSelector = ({ 
  currentLanguage, 
  onLanguageChange 
}: LanguageSelectorProps) => {
  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger className="flex items-center gap-1 p-1.5 rounded-full hover:bg-gray-200 transition-colors">
          <Avatar className="h-5 w-5">
            {currentLanguage === "pt-BR" && (
              <AvatarImage src="/lovable-uploads/66ca5442-fb91-42b9-abb3-7c2ceb3ba5a1.png" alt="Brazil flag" className="object-cover" />
            )}
            {currentLanguage === "en" && (
              <AvatarImage src="/lovable-uploads/9a55e44d-5842-4208-93cf-8d6025d06407.png" alt="USA flag" className="object-cover" />
            )}
            {currentLanguage === "es" && (
              <AvatarImage src="/lovable-uploads/c6e7cb09-f147-4c06-b58e-057794b3452e.png" alt="Spain flag" className="object-cover" />
            )}
            <AvatarFallback>
              {currentLanguage === "pt-BR" && "BR"}
              {currentLanguage === "en" && "EN"}
              {currentLanguage === "es" && "ES"}
            </AvatarFallback>
          </Avatar>
          <ChevronDown size={12} className="text-gray-600" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-32">
          <DropdownMenuItem onClick={() => onLanguageChange("pt-BR")} className="cursor-pointer">
            <Avatar className="h-5 w-5 mr-2">
              <AvatarImage src="/lovable-uploads/66ca5442-fb91-42b9-abb3-7c2ceb3ba5a1.png" alt="Brazil flag" className="object-cover" />
              <AvatarFallback>BR</AvatarFallback>
            </Avatar>
            <span>Português (BR)</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onLanguageChange("en")} className="cursor-pointer">
            <Avatar className="h-5 w-5 mr-2">
              <AvatarImage src="/lovable-uploads/9a55e44d-5842-4208-93cf-8d6025d06407.png" alt="USA flag" className="object-cover" />
              <AvatarFallback>EN</AvatarFallback>
            </Avatar>
            <span>English</span>
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => onLanguageChange("es")} className="cursor-pointer">
            <Avatar className="h-5 w-5 mr-2">
              <AvatarImage src="/lovable-uploads/c6e7cb09-f147-4c06-b58e-057794b3452e.png" alt="Spain flag" className="object-cover" />
              <AvatarFallback>ES</AvatarFallback>
            </Avatar>
            <span>Español</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
