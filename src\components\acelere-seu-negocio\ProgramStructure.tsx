
import { CheckCircle, Clock, Calendar, BadgeCheck } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const ProgramStructure = () => {
  const programModules = [
    {
      icon: Clock,
      title: "<PERSON><PERSON><PERSON>",
      details: [
        "3 meses de imersão",
        "24 aulas online",
        "12 workshops práticos",
        "6 mentorias individuais"
      ]
    },
    {
      icon: Calendar,
      title: "Cronograma",
      details: [
        "Módulo 1: Diagnóstico e Estratégia",
        "Módulo 2: Otimização e Escalabilidade",
        "Módulo 3: Captação e Crescimento",
        "Demo Day com investidores"
      ]
    },
    {
      icon: BadgeCheck,
      title: "Certificação",
      details: [
        "Certificado reconhecido pelo mercado",
        "Acesso vitalício à comunidade",
        "Networking com mentores e investidores",
        "Participação em eventos exclusivos"
      ]
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Estrutura do Programa
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Um programa completo e intensivo de 3 meses, combinando conteúdo online, mentorias presenciais e workshops práticos
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {programModules.map((item, idx) => (
            <Card key={idx} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardContent className="p-8">
                <div className="w-16 h-16 rounded-full bg-brand/10 flex items-center justify-center mb-6 mx-auto">
                  <item.icon className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-2xl font-bold text-center mb-6">{item.title}</h3>
                <ul className="space-y-4">
                  {item.details.map((detail, i) => (
                    <li key={i} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-brand-accent mt-0.5 mr-3 flex-shrink-0" />
                      <span className="text-gray-800">{detail}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProgramStructure;
