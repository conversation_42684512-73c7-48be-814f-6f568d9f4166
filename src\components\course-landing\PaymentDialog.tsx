
import { CreditCard } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

interface PaymentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onPaymentComplete: () => void;
  course: any;
}

const PaymentDialog = ({ open, onOpenChange, onPaymentComplete, course }: PaymentDialogProps) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Finalizar Pagamento</DialogTitle>
          <DialogDescription>
            Complete o pagamento para ter acesso ao curso {course.title}.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="border rounded-md p-4">
            <h3 className="font-medium mb-2">{course.title}</h3>
            <p className="text-sm text-gray-500 mb-2">{course.subtitle}</p>
            <div className="flex justify-between items-center">
              <span className="font-semibold">{course.price.discounted}</span>
              <span className="text-xs text-green-600">ou {course.price.installments}</span>
            </div>
          </div>
          <div className="grid gap-2">
            <label htmlFor="cardNumber" className="text-sm font-medium">Número do Cartão</label>
            <input 
              id="cardNumber" 
              type="text" 
              placeholder="0000 0000 0000 0000"
              className="border rounded-md px-3 py-2 text-sm"
            />
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="expiry" className="text-sm font-medium">Validade</label>
              <input 
                id="expiry" 
                type="text" 
                placeholder="MM/AA"
                className="border rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="grid gap-2">
              <label htmlFor="cvc" className="text-sm font-medium">CVC</label>
              <input 
                id="cvc" 
                type="text" 
                placeholder="123"
                className="border rounded-md px-3 py-2 text-sm"
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>Cancelar</Button>
          <Button className="bg-brand hover:bg-brand-secondary" onClick={onPaymentComplete}>
            <CreditCard className="mr-2 h-4 w-4" />
            Finalizar Pagamento
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default PaymentDialog;
