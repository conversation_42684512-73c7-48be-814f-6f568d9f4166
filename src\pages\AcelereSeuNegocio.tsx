
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import Hero from "@/components/Hero";
import { Rocket, Star } from "lucide-react";
import TestimonialsSection from "@/components/learning-experience/TestimonialsSection";
import ProgramOverview from "@/components/acelere-seu-negocio/ProgramOverview";
import ProgramStructure from "@/components/acelere-seu-negocio/ProgramStructure";
import MentorsSection from "@/components/acelere-seu-negocio/MentorsSection";
import PricingSection from "@/components/acelere-seu-negocio/PricingSection";
import FaqSection from "@/components/acelere-seu-negocio/FaqSection";

const AcelereSeuNegocio = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Acelere seu Negócio | ATAC";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      {/* Hero Section */}
      <Hero 
        smallText="PROGRAMA DE ACELERAÇÃO"
        title="<span>Acelere</span> seu <span class='text-brand-accent'>Negócio</span>"
        description="Programa completo de aceleração para startups com produto validado que buscam escalar de forma rápida e sustentável."
        primaryButtonText="Inscreva-se Agora"
        primaryButtonLink="/inscricao-programa"
        secondaryButtonText="Fale com um Consultor"
        secondaryButtonLink="/contato"
        showCarousel={false}
        stats={[
          { text: "+500 Negócios Acelerados", icon: Rocket },
          { text: "97% de Satisfação", icon: Star }
        ]}
      />

      {/* Program Overview */}
      <ProgramOverview />

      {/* Program Structure */}
      <ProgramStructure />

      {/* Mentors */}
      <MentorsSection />

      {/* Testimonials */}
      <TestimonialsSection />

      {/* Pricing & CTA */}
      <PricingSection />

      {/* FAQ */}
      <FaqSection />

      <Footer />
    </div>
  );
};

export default AcelereSeuNegocio;
