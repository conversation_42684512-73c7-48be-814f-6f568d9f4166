
import { Link } from "react-router-dom";
import { <PERSON>, BookOpen, Users, Star, Calendar, Award, Brain, ArrowRight } from "lucide-react";

interface CourseHeroProps {
  course: any;
  onEnrollmentClick?: () => void;
}

const CourseHero = ({ course, onEnrollmentClick }: CourseHeroProps) => {
  // Check if the course is Startup Bootcamp, Startup Revolution, or AI na Prática to apply specific layout
  const isStartupBootcamp = course.id === "startup-bootcamp";
  const isStartupRevolution = course.id === "startup-revolution";
  const isAINaPratica = course.id === "ai-na-pratica";
  const hasSpecialLayout = isStartupBootcamp || isStartupRevolution || isAINaPratica;

  return (
    <section className={`pt-20 ${hasSpecialLayout ? "" : "bg-gradient-to-r from-brand to-brand-secondary"} text-white`}>
      {hasSpecialLayout ? (
        // Layout for courses with special layout
        <div 
          className="relative w-full bg-cover bg-center" 
          style={{ 
            backgroundImage: `url(${course.heroImage})`,
            minHeight: "600px"
          }}
        >
          <div className={`absolute inset-0 ${isAINaPratica ? 'bg-gradient-to-r from-black/30 to-transparent' : 'bg-gradient-to-r from-black/60 to-transparent'}`}></div>
          <div className="container mx-auto px-4 py-24 relative z-10">
            <div className="max-w-xl">
              {isStartupBootcamp ? (
                // Startup Bootcamp specific content
                <>
                  <div className="inline-block bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full mb-4">
                    {course.type}
                  </div>
                  <h1 className="font-montserrat text-5xl md:text-6xl lg:text-7xl font-bold mb-1">
                    <span className="text-white">Startup</span>
                  </h1>
                  <h1 className="font-montserrat text-5xl md:text-6xl lg:text-7xl font-bold mb-4">
                    <span className="text-yellow-400">Bootcamp</span>
                  </h1>
                  <h2 className="text-2xl md:text-3xl font-medium mb-2">
                    Estação de base
                  </h2>
                  <p className="text-xl md:text-2xl text-white/90 mb-8">
                    da ideia ao modelo e à estratégia do negócio
                  </p>
                </>
              ) : isStartupRevolution ? (
                // Startup Revolution specific content
                <>
                  <div className="inline-block bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full mb-4">
                    {course.type}
                  </div>
                  <h1 className="font-montserrat text-5xl md:text-6xl lg:text-7xl font-bold mb-4">
                    <span className="text-white">Startup</span><br />
                    <span className="text-white">REVOLUTION</span>
                  </h1>
                  <p className="text-xl md:text-2xl text-white/90 mb-8">
                    Você tem uma ideia inovadora<br />
                    e precisa de ajuda para decolar?
                  </p>
                </>
              ) : (
                // AI na Prática specific content - now with AgileThink branding
                <div className="h-80">
                  {/* Espaço para compensar a imagem que já contém as informações principais */}
                </div>
              )}
              
              <div className={`flex flex-wrap gap-6 mb-8 ${isAINaPratica ? 'mt-72' : ''}`}>
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>{course.duration}</span>
                </div>
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  <span>{course.lessons} aulas</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  <span>{course.students}+ alunos</span>
                </div>
                <div className="flex items-center">
                  <div className="flex">
                    {Array(5).fill(0).map((_, i) => (
                      <Star key={i} className={`h-5 w-5 ${i < Math.floor(course.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-white/40'}`} />
                    ))}
                  </div>
                  <span className="ml-2">{course.rating}</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                {isStartupRevolution ? (
                  <a 
                    href="#enrollment" 
                    className="bg-amber-500 hover:bg-amber-600 text-black px-8 py-4 rounded-full font-medium text-lg flex items-center justify-center"
                    onClick={(e) => {
                      if (onEnrollmentClick) {
                        e.preventDefault();
                        onEnrollmentClick();
                      }
                    }}
                  >
                    Participe do nosso webinar
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </a>
                ) : isAINaPratica ? (
                  <a 
                    href="#enrollment" 
                    className="bg-purple-600 hover:bg-purple-700 text-white px-8 py-4 rounded-full font-medium text-lg flex items-center justify-center"
                    onClick={(e) => {
                      if (onEnrollmentClick) {
                        e.preventDefault();
                        onEnrollmentClick();
                      }
                    }}
                  >
                    Inscreva-se Agora
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </a>
                ) : (
                  <a 
                    href="#enrollment" 
                    className="bg-yellow-500 hover:bg-yellow-600 text-black px-8 py-4 rounded-md font-medium text-lg flex items-center justify-center"
                    onClick={(e) => {
                      if (onEnrollmentClick) {
                        e.preventDefault();
                        onEnrollmentClick();
                      }
                    }}
                  >
                    Inscreva-se Agora
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </a>
                )}
                
                <a href="#curriculum" className="bg-white/20 hover:bg-white/30 text-white px-8 py-4 rounded-md font-medium text-lg flex items-center justify-center">
                  Ver Grade Curricular
                </a>
              </div>
              
              {isStartupBootcamp && (
                <div className="flex mt-12 gap-6">
                  <img src="/lovable-uploads/c6e7cb09-f147-4c06-b58e-057794b3452e.png" alt="UniversitasLAB" className="h-12" />
                  <img src="/lovable-uploads/9f1d2520-fa14-40df-9615-72abd62459bc.png" alt="Apple Authorised Training" className="h-12" />
                </div>
              )}
              
              {isStartupRevolution && (
                <div className="flex mt-12 gap-6">
                  <img src="/lovable-uploads/c6e7cb09-f147-4c06-b58e-057794b3452e.png" alt="AmazonasCap" className="h-10" />
                </div>
              )}
              
              {isAINaPratica && (
                <div className="flex mt-12 gap-6">
                  <img src="/lovable-uploads/c6e7cb09-f147-4c06-b58e-057794b3452e.png" alt="AgileThink" className="h-10" />
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        // Layout padrão para os outros cursos
        <div className="container mx-auto px-4 py-12 md:py-20">
          <div className="flex flex-col md:flex-row items-center">
            <div className="md:w-1/2 mb-8 md:mb-0 md:pr-12">
              <div className="inline-block bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full mb-4">
                {course.type}
              </div>
              <h1 className="font-montserrat text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
                {course.title}
              </h1>
              <p className="text-xl md:text-2xl text-white/90 mb-6">
                {course.subtitle}
              </p>
              <p className="text-lg text-white/80 mb-8">
                {course.description}
              </p>
              
              <div className="flex flex-wrap gap-6 mb-8">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  <span>{course.duration}</span>
                </div>
                <div className="flex items-center">
                  <BookOpen className="h-5 w-5 mr-2" />
                  <span>{course.lessons} aulas</span>
                </div>
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  <span>{course.students}+ alunos</span>
                </div>
                <div className="flex items-center">
                  <div className="flex">
                    {Array(5).fill(0).map((_, i) => (
                      <Star key={i} className={`h-5 w-5 ${i < Math.floor(course.rating) ? 'fill-yellow-400 text-yellow-400' : 'text-white/40'}`} />
                    ))}
                  </div>
                  <span className="ml-2">{course.rating}</span>
                </div>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <a 
                  href="#enrollment" 
                  className="bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-4 rounded-md font-medium text-lg flex items-center justify-center"
                  onClick={(e) => {
                    if (onEnrollmentClick) {
                      e.preventDefault();
                      onEnrollmentClick();
                    }
                  }}
                >
                  Inscreva-se Agora
                  <ArrowRight className="ml-2 h-5 w-5" />
                </a>
                <a href="#curriculum" className="bg-white/10 hover:bg-white/20 text-white px-8 py-4 rounded-md font-medium text-lg flex items-center justify-center">
                  Ver Grade Curricular
                </a>
              </div>
            </div>
            
            <div className="md:w-1/2">
              <div className="relative rounded-lg overflow-hidden shadow-2xl">
                <img 
                  src={course.heroImage} 
                  alt={course.title}
                  className="w-full h-auto"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-brand/50 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>
      )}
      
      {/* Stats Bar */}
      <div className={`${hasSpecialLayout ? 'bg-black/80' : 'bg-white/10 backdrop-blur-md'} py-6`}>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="flex items-center justify-center md:justify-start">
              <Calendar className={`h-8 w-8 mr-4 ${
                isAINaPratica ? 'text-purple-400' : 
                isStartupRevolution ? 'text-purple-400' : 
                isStartupBootcamp ? 'text-yellow-400' : 
                'text-brand-accent'
              }`} />
              <div>
                <p className="text-white/70 text-sm">Próxima Turma</p>
                <p className="font-medium">{course.nextClasses[0].date}</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <Users className={`h-8 w-8 mr-4 ${
                isAINaPratica ? 'text-purple-400' : 
                isStartupRevolution ? 'text-purple-400' : 
                isStartupBootcamp ? 'text-yellow-400' : 
                'text-brand-accent'
              }`} />
              <div>
                <p className="text-white/70 text-sm">Vagas Limitadas</p>
                <p className="font-medium">Restam {course.nextClasses[0].spots} vagas</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <Award className={`h-8 w-8 mr-4 ${
                isAINaPratica ? 'text-purple-400' : 
                isStartupRevolution ? 'text-purple-400' : 
                isStartupBootcamp ? 'text-yellow-400' : 
                'text-brand-accent'
              }`} />
              <div>
                <p className="text-white/70 text-sm">Certificado</p>
                <p className="font-medium">Reconhecido pelo Mercado</p>
              </div>
            </div>
            <div className="flex items-center justify-center md:justify-start">
              <Brain className={`h-8 w-8 mr-4 ${
                isAINaPratica ? 'text-purple-400' : 
                isStartupRevolution ? 'text-purple-400' : 
                isStartupBootcamp ? 'text-yellow-400' : 
                'text-brand-accent'
              }`} />
              <div>
                <p className="text-white/70 text-sm">Nível</p>
                <p className="font-medium">Iniciante a Avançado</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CourseHero;
