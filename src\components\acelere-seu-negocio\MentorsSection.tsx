
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { mentors } from "@/data/mentors";

const MentorsSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Nossos Mentores
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Empreendedores experientes e especialistas de mercado que já percorreram o caminho do sucesso
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {mentors.slice(0, 4).map((mentor) => (
            <div key={mentor.id} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
              <div className="h-64 overflow-hidden">
                <img 
                  src={mentor.image} 
                  alt={mentor.name} 
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-6">
                <h3 className="font-bold text-xl mb-1">{mentor.name}</h3>
                <p className="text-brand text-sm mb-1">{mentor.role}</p>
                <p className="text-gray-600 text-sm mb-4">{mentor.company}</p>
                <Link to={`/mentor/${mentor.id}`}>
                  <Button className="w-full bg-brand hover:bg-brand-secondary">
                    Ver Perfil
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Link to="/mentoria">
            <Button variant="outline" className="border-brand text-brand hover:bg-brand hover:text-white">
              Ver Todos os Mentores
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default MentorsSection;
