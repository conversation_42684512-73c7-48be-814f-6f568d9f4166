
import { useEffect } from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";

const Inscrever = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Inscrever-se | ATAC";
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          <div className="max-w-xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
            <div className="px-6 py-8">
              <div className="text-center mb-8">
                <h1 className="font-montserrat text-2xl font-bold text-gray-900 mb-2">
                  Crie sua conta na ATAC
                </h1>
                <p className="text-gray-600">
                  Junte-se a nossa comunidade de empreendedores
                </p>
              </div>
              
              <form className="space-y-5">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-5">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 mb-1">
                      Nome
                    </label>
                    <input
                      id="firstName"
                      type="text"
                      className="custom-input"
                      placeholder="Seu nome"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="lastName" className="block text-sm font-medium text-gray-700 mb-1">
                      Sobrenome
                    </label>
                    <input
                      id="lastName"
                      type="text"
                      className="custom-input"
                      placeholder="Seu sobrenome"
                    />
                  </div>
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                    E-mail
                  </label>
                  <input
                    id="email"
                    type="email"
                    className="custom-input"
                    placeholder="<EMAIL>"
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
                    Telefone
                  </label>
                  <input
                    id="phone"
                    type="tel"
                    className="custom-input"
                    placeholder="(00) 00000-0000"
                  />
                </div>
                
                <div>
                  <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                    Empresa / Startup (Opcional)
                  </label>
                  <input
                    id="company"
                    type="text"
                    className="custom-input"
                    placeholder="Nome da sua empresa"
                  />
                </div>
                
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                    Senha
                  </label>
                  <input
                    id="password"
                    type="password"
                    className="custom-input"
                    placeholder="Crie uma senha forte"
                  />
                </div>
                
                <div>
                  <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                    Confirmar Senha
                  </label>
                  <input
                    id="confirmPassword"
                    type="password"
                    className="custom-input"
                    placeholder="Confirme sua senha"
                  />
                </div>
                
                <div className="flex items-start">
                  <div className="flex items-center h-5">
                    <input
                      id="terms"
                      type="checkbox"
                      className="h-4 w-4 text-brand focus:ring-brand border-gray-300 rounded"
                    />
                  </div>
                  <div className="ml-3 text-sm">
                    <label htmlFor="terms" className="text-gray-700">
                      Eu concordo com os{" "}
                      <Link to="/termos" className="text-brand hover:text-brand-secondary">
                        Termos de Serviço
                      </Link>{" "}
                      e{" "}
                      <Link to="/privacidade" className="text-brand hover:text-brand-secondary">
                        Política de Privacidade
                      </Link>
                    </label>
                  </div>
                </div>
                
                <Button className="w-full bg-brand hover:bg-brand-secondary">
                  Criar Conta
                </Button>
              </form>
              
              <div className="mt-6 text-center">
                <p className="text-gray-600 text-sm">
                  Já tem uma conta?{" "}
                  <Link to="/login" className="text-brand hover:text-brand-secondary font-medium">
                    Faça login
                  </Link>
                </p>
              </div>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Inscrever;
