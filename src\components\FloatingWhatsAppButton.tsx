
import { MessageCircle } from "lucide-react";
import { cn } from "@/lib/utils";

interface FloatingWhatsAppButtonProps {
  whatsappLink?: string;
  className?: string;
}

const FloatingWhatsAppButton = ({
  whatsappLink = "https://chat.whatsapp.com/G9zX8sBbjJ13vbLO0qcQir",
  className,
}: FloatingWhatsAppButtonProps) => {
  return (
    <a
      href={whatsappLink}
      target="_blank"
      rel="noopener noreferrer"
      className={cn(
        "fixed bottom-6 right-6 z-40 flex items-center justify-center w-12 h-12 rounded-full text-white bg-[#25D366] hover:bg-[#20BA5C] shadow-md transition-all duration-300 hover:scale-110",
        className
      )}
      aria-label="Fale Conosco pelo WhatsApp"
    >
      <MessageCircle size={22} />
    </a>
  );
};

export default FloatingWhatsAppButton;
