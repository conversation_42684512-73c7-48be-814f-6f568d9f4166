
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Quote } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const testimonials = [
  {
    id: 1,
    content: "O programa de aceleração da EmpreenderAcademy foi um divisor de águas para minha startup. A combinação de mentoria qualificada e acesso a investidores nos permitiu triplicar nosso faturamento em apenas 6 meses.",
    author: "<PERSON>",
    role: "CEO, TechSolve",
    image: "https://i.pravatar.cc/150?img=32"
  },
  {
    id: 2,
    content: "Participar dos cursos da EmpreenderAcademy transformou minha visão sobre empreendedorismo. O conteúdo prático e as conexões que fiz me deram confiança para pivotar meu modelo de negócio e encontrar um caminho sustentável.",
    author: "<PERSON>",
    role: "Fundador, EcoDelivery",
    image: "https://i.pravatar.cc/150?img=11"
  },
  {
    id: 3,
    content: "A metodologia hands-on e a rede de contatos que construí durante o programa foram essenciais para conseguirmos nossa primeira rodada de investimento. Recomendo para todo empreendedor que quer acelerar seu crescimento.",
    author: "Mariana Almeida",
    role: "Co-fundadora, FinHealth",
    image: "https://i.pravatar.cc/150?img=5"
  }
];

const Testimonials = () => {
  const [activeIndex, setActiveIndex] = useState(0);

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  return (
    <section className="py-20 bg-brand-accent">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-white mb-4">
            O que dizem nossos alunos
          </h2>
          <p className="text-lg text-white/90 max-w-2xl mx-auto">
            Histórias reais de empreendedores que transformaram seus negócios com nossos programas.
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-xl p-8 md:p-10 shadow-xl relative">
            <Quote className="absolute top-6 left-6 h-12 w-12 text-brand-light opacity-50" />
            
            <div className="relative z-10">
              <p className="text-xl text-gray-800 mb-8 pl-10">
                {testimonials[activeIndex].content}
              </p>
              
              <div className="flex items-center">
                <img 
                  src={testimonials[activeIndex].image} 
                  alt={testimonials[activeIndex].author}
                  className="w-16 h-16 rounded-full object-cover mr-4"
                />
                <div>
                  <h4 className="font-medium text-gray-900">{testimonials[activeIndex].author}</h4>
                  <p className="text-gray-600">{testimonials[activeIndex].role}</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex justify-center mt-8 space-x-4">
            <Button 
              variant="outline" 
              size="icon" 
              className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
              onClick={prevTestimonial}
            >
              <ArrowLeft className="h-5 w-5" />
            </Button>
            
            {testimonials.map((_, index) => (
              <button
                key={index}
                className={`w-3 h-3 rounded-full ${
                  index === activeIndex ? "bg-white" : "bg-white/40"
                }`}
                onClick={() => setActiveIndex(index)}
              />
            ))}
            
            <Button 
              variant="outline" 
              size="icon"
              className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
              onClick={nextTestimonial}
            >
              <ArrowRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;
