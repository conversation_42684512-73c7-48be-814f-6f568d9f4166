import { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import { Link, useLocation } from "react-router-dom";
import { Menu, X, Search } from "lucide-react";
import { useIsMobile } from "@/hooks/use-mobile";
import { ExpandedMenu } from "./navbar/ExpandedMenu";
import { LanguageSelector } from "./navbar/LanguageSelector";
import { SearchBar } from "./navbar/SearchBar";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState<"pt-BR" | "en" | "es">("pt-BR");
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const location = useLocation();
  const menuRef = useRef<HTMLDivElement>(null);
  const isMobile = useIsMobile();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleSearch = () => {
    setIsSearchOpen(!isSearchOpen);
  };

  const changeLanguage = (language: "pt-BR" | "en" | "es") => {
    setCurrentLanguage(language);
  };

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 20) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsMenuOpen(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    document.addEventListener("mousedown", handleClickOutside);
    
    return () => {
      window.removeEventListener("scroll", handleScroll);
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  return (
    <>
      <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${scrolled ? 'bg-white shadow-md py-2' : 'bg-white py-4'}`}>
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <button 
                className="flex items-center text-gray-800 font-medium text-sm"
                onClick={toggleMenu}
                aria-label={isMenuOpen ? "Close menu" : "Open menu"}
              >
                {isMenuOpen ? (
                  <X size={20} className="mr-1" />
                ) : (
                  <Menu size={20} className="mr-1" />
                )}
                <span className="text-xs">MENU</span>
              </button>
              
              <Link to="/" className="flex items-center">
                <img
                  src="/logo.png"
                  alt="ATAC Academy"
                  className="h-8 w-auto"
                />
              </Link>
            </div>

            {/* <div className="flex items-center gap-3">
              <button 
                onClick={toggleSearch}
                className="p-1.5 rounded-full hover:bg-gray-200 transition-colors"
                aria-label="Search"
              >
                <Search size={16} className="text-gray-600" />
              </button>
              
              <div className="flex items-center">
                <LanguageSelector 
                  currentLanguage={currentLanguage}
                  onLanguageChange={changeLanguage}
                />
              </div>
              
              <Link to="/login" className="ml-2">
                <Button className="bg-brand-accent hover:bg-brand-accent/90 text-white text-xs py-1.5 h-auto whitespace-nowrap">
                  Login
                </Button>
              </Link>
            </div> */}
          </div>
        </div>
      </nav>

      {isSearchOpen && (
        <SearchBar isOpen={isSearchOpen} onClose={() => setIsSearchOpen(false)} isMobile={isMobile} />
      )}

      {isMenuOpen && <ExpandedMenu onClose={() => setIsMenuOpen(false)} />}
    </>
  );
};

export default Navbar;
