
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Calendar, Clock, MessageCircle, CheckCircle } from "lucide-react";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/data/mentors";

interface BookingWidgetProps {
  mentor: Mentor;
  availableDates: {
    date: Date;
    dayName: string;
    formattedDate: string;
  }[];
  availableTimeSlots: string[];
}

const BookingWidget = ({ mentor, availableDates, availableTimeSlots }: BookingWidgetProps) => {
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [selectedTime, setSelectedTime] = useState<string | null>(null);
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isPaymentComplete, setIsPaymentComplete] = useState(false);

  const handleBookingClick = () => {
    setIsPaymentDialogOpen(true);
  };

  const handlePaymentSubmit = () => {
    setIsProcessing(true);
    // Simulate payment processing
    setTimeout(() => {
      setIsProcessing(false);
      setIsPaymentComplete(true);
    }, 2000);
  };

  const resetPaymentFlow = () => {
    setIsPaymentDialogOpen(false);
    setIsPaymentComplete(false);
  };

  return (
    <>
      <div className="bg-white rounded-xl shadow-sm border p-6 sticky top-24">
        <h2 className="text-xl font-bold mb-6">Agende uma Mentoria</h2>
        
        <div className="mb-6">
          <div className="flex justify-between items-center mb-3">
            <h3 className="font-medium text-gray-800">Duração:</h3>
            <span className="bg-brand-light text-brand text-sm font-medium px-3 py-1 rounded-full">
              60 minutos
            </span>
          </div>
          <div className="flex justify-between items-center mb-6">
            <h3 className="font-medium text-gray-800">Valor:</h3>
            <div className="text-right">
              <span className="font-bold text-xl text-gray-900">
                {mentor.sessionPrice ? `R$ ${mentor.sessionPrice},00` : "R$ 350,00"}
              </span>
              <p className="text-xs text-gray-500">Pagamento único</p>
            </div>
          </div>
        </div>
        
        <div className="mb-6">
          <h3 className="font-medium text-gray-800 mb-3">Selecione uma data:</h3>
          <div className="grid grid-cols-3 gap-2">
            {availableDates.slice(0, 6).map((dateObj, idx) => (
              <button
                key={idx}
                className={`p-2 text-center rounded-md border text-sm ${
                  selectedDate === dateObj.formattedDate
                    ? 'bg-brand text-white border-brand'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => setSelectedDate(dateObj.formattedDate)}
              >
                <div className="font-medium">{dateObj.formattedDate}</div>
                <div className="text-xs truncate">{dateObj.dayName.slice(0, 3)}</div>
              </button>
            ))}
          </div>
        </div>
        
        {selectedDate && (
          <div className="mb-6">
            <h3 className="font-medium text-gray-800 mb-3">Selecione um horário:</h3>
            <div className="grid grid-cols-3 gap-2">
              {availableTimeSlots.map((time, idx) => (
                <button
                  key={idx}
                  className={`p-2 text-center rounded-md border text-sm ${
                    selectedTime === time
                      ? 'bg-brand text-white border-brand'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  onClick={() => setSelectedTime(time)}
                >
                  {time}
                </button>
              ))}
            </div>
          </div>
        )}
        
        <Button 
          className="w-full bg-brand hover:bg-brand-secondary mb-4"
          disabled={!selectedDate || !selectedTime}
          onClick={handleBookingClick}
        >
          Agendar Mentoria
        </Button>
        
        <div className="space-y-4 border-t pt-4">
          <div className="flex items-start gap-2">
            <Calendar className="h-5 w-5 text-brand flex-shrink-0 mt-0.5" />
            <p className="text-sm text-gray-600">Disponível nos dias: {mentor.availability.join(', ')}</p>
          </div>
          <div className="flex items-start gap-2">
            <MessageCircle className="h-5 w-5 text-brand flex-shrink-0 mt-0.5" />
            <p className="text-sm text-gray-600">Sessões online via Zoom ou Google Meet</p>
          </div>
          <div className="flex items-start gap-2">
            <Clock className="h-5 w-5 text-brand flex-shrink-0 mt-0.5" />
            <p className="text-sm text-gray-600">Resposta em até 24 horas</p>
          </div>
        </div>
      </div>

      {/* Payment Dialog */}
      <Dialog open={isPaymentDialogOpen} onOpenChange={setIsPaymentDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {isPaymentComplete ? "Pagamento Confirmado!" : "Confirmar Agendamento"}
            </DialogTitle>
            <DialogDescription>
              {isPaymentComplete 
                ? "Sua mentoria foi agendada com sucesso." 
                : `Mentoria com ${mentor.name} em ${selectedDate} às ${selectedTime}`}
            </DialogDescription>
          </DialogHeader>

          {!isPaymentComplete ? (
            <>
              <div className="space-y-4 py-4">
                <div className="border rounded-lg p-4">
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Mentor:</span>
                    <span className="font-medium">{mentor.name}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Data:</span>
                    <span className="font-medium">{selectedDate}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Horário:</span>
                    <span className="font-medium">{selectedTime}</span>
                  </div>
                  <div className="flex justify-between mb-2">
                    <span className="text-gray-600">Duração:</span>
                    <span className="font-medium">60 minutos</span>
                  </div>
                  <div className="flex justify-between pt-2 border-t">
                    <span className="font-medium">Total:</span>
                    <span className="font-bold text-brand">
                      {mentor.sessionPrice ? `R$ ${mentor.sessionPrice},00` : "R$ 350,00"}
                    </span>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <h4 className="font-medium mb-3">Método de Pagamento</h4>
                  <div className="space-y-3">
                    <div className="flex items-center">
                      <input
                        id="payment-pix"
                        name="payment-method"
                        type="radio"
                        className="h-4 w-4 text-brand"
                        defaultChecked
                      />
                      <label htmlFor="payment-pix" className="ml-2">
                        PIX
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="payment-card"
                        name="payment-method"
                        type="radio"
                        className="h-4 w-4 text-brand"
                      />
                      <label htmlFor="payment-card" className="ml-2">
                        Cartão de Crédito
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="payment-boleto"
                        name="payment-method"
                        type="radio"
                        className="h-4 w-4 text-brand"
                      />
                      <label htmlFor="payment-boleto" className="ml-2">
                        Boleto Bancário
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <DialogFooter>
                <Button 
                  variant="outline" 
                  onClick={() => setIsPaymentDialogOpen(false)}
                  className="mr-2"
                >
                  Cancelar
                </Button>
                <Button 
                  className="bg-brand hover:bg-brand-secondary"
                  onClick={handlePaymentSubmit}
                  disabled={isProcessing}
                >
                  {isProcessing ? "Processando..." : "Confirmar Pagamento"}
                </Button>
              </DialogFooter>
            </>
          ) : (
            <>
              <div className="flex flex-col items-center justify-center py-6">
                <CheckCircle className="h-16 w-16 text-green-500 mb-4" />
                <p className="text-lg font-medium text-center">
                  Sua mentoria foi agendada com sucesso!
                </p>
                <p className="text-gray-500 text-center mt-2">
                  Você receberá um e-mail com os detalhes da sessão e como se preparar.
                </p>
              </div>
              <DialogFooter>
                <Button 
                  className="bg-brand hover:bg-brand-secondary w-full"
                  onClick={resetPaymentFlow}
                >
                  Fechar
                </Button>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default BookingWidget;
