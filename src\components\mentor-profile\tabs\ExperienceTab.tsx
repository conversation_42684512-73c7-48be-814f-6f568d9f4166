
import { Briefcase, BookOpen, Award } from "lucide-react";

interface ExperienceTabProps {
  mentor: any;
}

const ExperienceTab = ({ mentor }: ExperienceTabProps) => {
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-xl font-bold mb-4">Experiência Profissional</h2>
        <div className="space-y-6">
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <Briefcase className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">{mentor.role}</h3>
              <p className="text-brand font-medium">{mentor.company}</p>
              <p className="text-gray-500 text-sm mb-2">Jan 2020 - Presente</p>
              <p className="text-gray-700">
                Responsável por liderar iniciativas estratégicas e impulsionar o crescimento 
                da empresa, com foco em inovação e desenvolvimento de novos negócios.
              </p>
            </div>
          </div>
          
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <Briefcase className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">Diretor de {mentor.expertise[0]}</h3>
              <p className="text-brand font-medium">Empresa Anterior S/A</p>
              <p className="text-gray-500 text-sm mb-2">Mar 2015 - Dez 2019</p>
              <p className="text-gray-700">
                Liderou equipes multidisciplinares e implementou estratégias que resultaram 
                em crescimento significativo da empresa no mercado nacional.
              </p>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Formação Acadêmica</h2>
        <div className="space-y-6">
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <BookOpen className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">MBA em Gestão de Negócios</h3>
              <p className="text-brand font-medium">Universidade Renomada</p>
              <p className="text-gray-500 text-sm">2012 - 2014</p>
            </div>
          </div>
          
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <BookOpen className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">Bacharelado em {
                mentor.expertise.includes("Tecnologia") || mentor.expertise.includes("Desenvolvimento") 
                  ? "Ciência da Computação" 
                  : mentor.expertise.includes("Marketing") 
                    ? "Marketing e Comunicação" 
                    : "Administração"
              }</h3>
              <p className="text-brand font-medium">Universidade Federal</p>
              <p className="text-gray-500 text-sm">2008 - 2012</p>
            </div>
          </div>
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-4">Certificações</h2>
        <div className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <Award className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">Certificação em {mentor.expertise[0]}</h3>
              <p className="text-gray-500 text-sm">Instituição Renomada, 2022</p>
            </div>
          </div>
          
          <div className="flex gap-4">
            <div className="flex-shrink-0 mt-1">
              <Award className="h-6 w-6 text-brand" />
            </div>
            <div>
              <h3 className="font-bold text-lg">Certificação em Liderança</h3>
              <p className="text-gray-500 text-sm">Professional Leadership Institute, 2020</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExperienceTab;
