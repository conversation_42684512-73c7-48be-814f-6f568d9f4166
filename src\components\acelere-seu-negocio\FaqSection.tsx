
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";

const faqItems = [
  {
    question: "Quem pode participar do programa?",
    answer: "O programa é destinado a startups que já possuem um produto ou serviço validado no mercado, com tração inicial e que estão buscando escalar suas operações. Idealmente, negócios que já possuem faturamento e uma equipe dedicada."
  },
  {
    question: "Qual é o formato do programa?",
    answer: "O programa tem duração de 3 meses e combina conteúdo online, workshops práticos e mentorias individuais. As atividades acontecem semanalmente, com flexibilidade para que você possa conciliar com a operação do seu negócio."
  },
  {
    question: "Quantas pessoas da minha empresa podem participar?",
    answer: "Cada inscrição permite a participação de até 2 pessoas da mesma empresa, preferencialmente os fundadores ou líderes principais do negócio."
  },
  {
    question: "O programa garante investimento para minha startup?",
    answer: "O programa não garante investimento, mas prepara sua empresa para captação e oferece networking com investidores, incluindo um Demo Day exclusivo ao final do programa. Muitos participantes conseguem captar recursos após a conclusão."
  },
  {
    question: "Existe um processo seletivo?",
    answer: "Sim, todas as empresas passam por um processo seletivo para garantir o fit com o programa e a qualidade das interações. Após a inscrição, realizamos uma entrevista para conhecer melhor o seu negócio."
  }
];

const FaqSection = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Perguntas Frequentes
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Tudo o que você precisa saber sobre o programa Acelere seu Negócio
          </p>
        </div>

        <div className="max-w-3xl mx-auto">
          <div className="space-y-6">
            {faqItems.map((faq, idx) => (
              <div key={idx} className="border-b border-gray-200 pb-6">
                <h3 className="text-xl font-bold mb-3">{faq.question}</h3>
                <p className="text-gray-700">{faq.answer}</p>
              </div>
            ))}
          </div>
          
          <div className="mt-12 text-center">
            <p className="text-lg mb-6">Ainda tem dúvidas? Entre em contato conosco</p>
            <Link to="/contato">
              <Button variant="outline" className="border-brand text-brand hover:bg-brand-light">
                Fale Conosco
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FaqSection;
