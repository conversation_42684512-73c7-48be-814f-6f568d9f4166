
import { <PERSON> } from "react-router-dom";
import { Star, Users, Award, Briefcase, ArrowLeft } from "lucide-react";

interface ProfileHeaderProps {
  mentor: any;
  achievements: {
    title: string;
    icon: React.ReactNode;
  }[];
}

const ProfileHeader = ({ mentor, achievements }: ProfileHeaderProps) => {
  return (
    <div className="bg-white shadow-sm border-b">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center gap-2 mb-6 text-gray-500">
          <Link to="/mentoria" className="flex items-center gap-1 hover:text-brand transition-colors">
            <ArrowLeft className="h-4 w-4" />
            <span>Voltar para mentores</span>
          </Link>
        </div>
        
        <div className="flex items-start gap-6 mb-6">
          <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-md flex-shrink-0">
            <img 
              src={mentor.image} 
              alt={mentor.name} 
              className="w-full h-full object-cover"
            />
          </div>
          
          <div>
            <h1 className="text-3xl font-bold mb-2">{mentor.name}</h1>
            <p className="text-brand font-medium mb-1">{mentor.role}</p>
            <p className="text-gray-600 mb-4">{mentor.company}</p>
            
            <div className="flex flex-wrap gap-2 mb-4">
              {mentor.expertise.map((skill: string, idx: number) => (
                <span 
                  key={idx} 
                  className="bg-brand-light text-brand text-sm font-medium px-3 py-1 rounded-full"
                >
                  {skill}
                </span>
              ))}
            </div>
            
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-1 text-yellow-500">
                <Star className="h-5 w-5 fill-yellow-500" />
                <span className="font-bold">{mentor.rating}</span>
              </div>
              <span className="text-gray-500">({mentor.sessions} sessões realizadas)</span>
            </div>
          </div>
        </div>
        
        <div className="flex flex-wrap gap-3 mb-8">
          {achievements.map((achievement, index) => (
            <div 
              key={index} 
              className="flex items-center gap-2 bg-gray-50 px-4 py-2 rounded-md"
            >
              {achievement.icon}
              <span className="text-sm font-medium">{achievement.title}</span>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
