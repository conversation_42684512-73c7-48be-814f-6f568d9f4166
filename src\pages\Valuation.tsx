import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowRight, Calendar, MessageSquare, BookOpen, Info, CreditCard } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import AIAssistant from "@/components/AIAssistant";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";

const Valuation = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  
  // Set page title and scroll to top on load
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Avaliação de Valuation | ATAC Academy";
  }, []);
  
  // Handle redirection to Maturity Assessment
  const handleMaturityRedirect = () => {
    navigate("/maturity");
  };
  
  // Handle scheduling of the consultation
  const handleScheduleConsultation = () => {
    toast({
      title: "Em breve!",
      description: "Nossa agenda será aberta em breve. Fique atento às suas notificações.",
    });
    // In a real implementation, this would redirect to a scheduling page
  };
  
  // Handle access to the micro-course
  const handleAccessMicroCourse = () => {
    setShowPaymentDialog(true);
  };

  // Handle payment completion
  const handlePaymentCompletion = () => {
    setShowPaymentDialog(false);
    // Redirect to the maturity assessment page after payment
    navigate("/maturity");
    toast({
      title: "Pagamento concluído!",
      description: "Bem-vindo ao nosso curso de Valuation. Você já tem acesso ao Assessment de Maturidade.",
      variant: "default",
    });
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 max-w-4xl">
          {/* Hero Section */}
          <div className="mb-10 text-center">
            <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Avaliação de <span className="text-[#00B8E4]">Valuation</span>
            </h1>
            <p className="text-lg text-gray-700 mb-6 max-w-2xl mx-auto">
              Descubra o valor potencial da sua startup e receba recomendações estratégicas para aumentar seu valuation através da metodologia ATAC.
            </p>
          </div>
          
          {/* ATAC Methodology Section */}
          <div className="bg-[#EAF9FF] p-8 rounded-xl mb-12">
            <h2 className="font-montserrat text-2xl font-semibold text-gray-900 mb-4 flex items-center gap-2">
              <Info className="h-6 w-6 text-[#00B8E4]" />
              Metodologia ATAC
            </h2>
            <p className="text-gray-700 mb-8">
              Nossa metodologia proprietária foi desenvolvida para ajudar startups a atingirem seu potencial máximo através de um processo estruturado de avaliação, mentoria e aceleração.
            </p>
            
            {/* Steps */}
            <div className="grid md:grid-cols-3 gap-6">
              {/* Step 1 */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="h-16 w-16 bg-[#E0F7FF] rounded-full flex items-center justify-center mb-4">
                  <span className="text-[#00B8E4] text-2xl font-bold">1</span>
                </div>
                <h3 className="font-semibold text-xl mb-2">Avaliação de Maturidade</h3>
                <p className="text-gray-600 mb-6">
                  Complete o questionário de avaliação de maturidade para identificarmos o estágio atual da sua startup.
                </p>
                <Button 
                  onClick={handleMaturityRedirect}
                  className="w-full bg-[#00B8E4] hover:bg-[#00a0c4] text-white"
                >
                  Iniciar Avaliação <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
              
              {/* Step 2 */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="h-16 w-16 bg-[#E0F7FF] rounded-full flex items-center justify-center mb-4">
                  <span className="text-[#00B8E4] text-2xl font-bold">2</span>
                </div>
                <h3 className="font-semibold text-xl mb-2">Consulta Estratégica</h3>
                <p className="text-gray-600 mb-6">
                  Agende uma conversa de 30 minutos com nossos especialistas para discutir os resultados da sua avaliação.
                </p>
                <Button 
                  onClick={handleScheduleConsultation}
                  variant="outline"
                  className="w-full border-[#00B8E4] text-[#00B8E4] hover:bg-[#E0F7FF] hover:text-[#00B8E4]"
                >
                  Agendar Consulta <Calendar className="ml-2 h-4 w-4" />
                </Button>
              </div>
              
              {/* Step 3 */}
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <div className="h-16 w-16 bg-[#E0F7FF] rounded-full flex items-center justify-center mb-4">
                  <span className="text-[#00B8E4] text-2xl font-bold">3</span>
                </div>
                <h3 className="font-semibold text-xl mb-2">Microcurso de Valuation</h3>
                <p className="text-gray-600 mb-6">
                  Acesse nosso conteúdo exclusivo sobre estratégias de valuation e preparação para rodadas de investimento.
                </p>
                <Button 
                  onClick={handleAccessMicroCourse}
                  variant="outline"
                  className="w-full border-[#00B8E4] text-[#00B8E4] hover:bg-[#E0F7FF] hover:text-[#00B8E4]"
                >
                  Acessar Microcurso <BookOpen className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
          
          {/* Benefits Section */}
          <div className="mb-12">
            <h2 className="font-montserrat text-2xl font-semibold text-gray-900 mb-6 text-center">
              Benefícios da Metodologia ATAC
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="flex gap-4">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Diagnóstico Preciso</h3>
                  <p className="text-gray-600 text-sm">Identificação detalhada dos pontos fortes e oportunidades de melhoria da sua startup.</p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Mentoria Especializada</h3>
                  <p className="text-gray-600 text-sm">Acesso a mentores experientes que já acompanharam centenas de startups.</p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Acesso a Investidores</h3>
                  <p className="text-gray-600 text-sm">Conexão com nossa rede de investidores-anjo, aceleradoras e fundos de venture capital.</p>
                </div>
              </div>
              
              <div className="flex gap-4">
                <div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center flex-shrink-0">
                  <svg className="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 mb-1">Plano de Ação Personalizado</h3>
                  <p className="text-gray-600 text-sm">Desenvolvimento de estratégia específica para o crescimento da sua startup.</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Call to Action */}
          <div className="bg-gray-50 p-8 rounded-xl text-center">
            <h2 className="font-montserrat text-2xl font-semibold text-gray-900 mb-4">
              Pronto para descobrir o valor da sua startup?
            </h2>
            <p className="text-gray-700 mb-6 max-w-xl mx-auto">
              Inicie hoje mesmo o processo de avaliação e dê um passo importante para aumentar o valuation da sua empresa.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button 
                onClick={handleMaturityRedirect}
                className="bg-[#00B8E4] hover:bg-[#00a0c4]"
              >
                Começar Avaliação de Maturidade
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
              <Button 
                onClick={handleScheduleConsultation}
                variant="outline"
              >
                Falar com um Especialista
                <MessageSquare className="ml-2 h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </main>
      <Footer />
      
      {/* Payment Dialog */}
      <Dialog open={showPaymentDialog} onOpenChange={setShowPaymentDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Finalizar Pagamento</DialogTitle>
            <DialogDescription>
              Complete o pagamento para ter acesso ao microcurso de Valuation e ao Assessment de Maturidade.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="border rounded-md p-4">
              <h3 className="font-medium mb-2">Microcurso de Valuation</h3>
              <p className="text-sm text-gray-500 mb-2">Acesso imediato a estratégias de valuation</p>
              <div className="flex justify-between items-center">
                <span className="font-semibold">R$ 197,00</span>
                <span className="text-xs text-green-600">Pagamento único</span>
              </div>
            </div>
            <div className="grid gap-2">
              <label htmlFor="cardNumber" className="text-sm font-medium">Número do Cartão</label>
              <input 
                id="cardNumber" 
                type="text" 
                placeholder="0000 0000 0000 0000"
                className="border rounded-md px-3 py-2 text-sm"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="expiry" className="text-sm font-medium">Validade</label>
                <input 
                  id="expiry" 
                  type="text" 
                  placeholder="MM/AA"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
              <div className="grid gap-2">
                <label htmlFor="cvc" className="text-sm font-medium">CVC</label>
                <input 
                  id="cvc" 
                  type="text" 
                  placeholder="123"
                  className="border rounded-md px-3 py-2 text-sm"
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowPaymentDialog(false)}>Cancelar</Button>
            <Button className="bg-[#00B8E4] hover:bg-[#00a0c4]" onClick={handlePaymentCompletion}>
              <CreditCard className="mr-2 h-4 w-4" />
              Finalizar Pagamento
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Add the AI Assistant */}
      <AIAssistant context="valuation" />
    </div>
  );
};

export default Valuation;
