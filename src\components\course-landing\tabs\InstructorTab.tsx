
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

interface InstructorTabProps {
  course: any;
  onEnrollmentClick?: () => void;
}

const InstructorTab = ({ course, onEnrollmentClick }: InstructorTabProps) => {
  return (
    <div className="container mx-auto px-4">
      <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-10">
        Conheça o Instrutor
      </h2>
      
      <div className="bg-gray-50 rounded-lg p-8 flex flex-col md:flex-row items-center md:items-start gap-8">
        <div className="w-40 h-40 rounded-full overflow-hidden flex-shrink-0">
          <img 
            src={course.instructor.image} 
            alt={course.instructor.name}
            className="w-full h-full object-cover"
          />
        </div>
        
        <div className="flex-1">
          <h3 className="font-montserrat text-2xl font-bold text-gray-900 mb-2">
            {course.instructor.name}
          </h3>
          <p className="text-brand-accent font-medium mb-4">
            {course.instructor.title}
          </p>
          <p className="text-lg text-gray-700 mb-6">
            {course.instructor.bio}
          </p>
          
          {onEnrollmentClick && (
            <div className="mt-4">
              <Button 
                onClick={onEnrollmentClick}
                className="bg-brand hover:bg-brand-secondary"
              >
                Garanta sua vaga com este instrutor
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default InstructorTab;
