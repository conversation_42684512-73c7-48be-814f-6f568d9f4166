
import { Star } from "lucide-react";

interface TestimonialsTabProps {
  mentor: any;
}

const TestimonialsTab = ({ mentor }: TestimonialsTabProps) => {
  return (
    <div>
      <h2 className="text-xl font-bold mb-6">O que estão dizendo sobre as mentorias</h2>
      <div className="space-y-6">
        <div className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center gap-2 mb-2">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
          </div>
          <p className="text-gray-700 mb-4 text-lg italic">
            "A mentoria com {mentor.name} foi transformadora para o meu negócio. 
            A experiência e insights compartilhados me ajudaram a superar desafios 
            que estavam impedindo o crescimento da minha empresa."
          </p>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3" 
                alt="Cliente" 
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h4 className="font-medium">Roberto Almeida</h4>
              <p className="text-sm text-gray-600">CEO, StartupX</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center gap-2 mb-2">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
          </div>
          <p className="text-gray-700 mb-4 text-lg italic">
            "Excelente experiência! {mentor.name.split(' ')[0]} tem uma capacidade incrível de 
            identificar problemas e propor soluções práticas. Depois da mentoria, 
            conseguimos aumentar nossas vendas em 40%."
          </p>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3" 
                alt="Cliente" 
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h4 className="font-medium">Camila Rodrigues</h4>
              <p className="text-sm text-gray-600">Fundadora, InnoTech</p>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-50 rounded-xl p-6">
          <div className="flex items-center gap-2 mb-2">
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
            <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
          </div>
          <p className="text-gray-700 mb-4 text-lg italic">
            "As sessões de mentoria com {mentor.name} são extremamente valiosas. 
            O conhecimento e a experiência que ele compartilha são práticos e 
            aplicáveis imediatamente. Recomendo fortemente."
          </p>
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 rounded-full bg-gray-300 overflow-hidden">
              <img 
                src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3" 
                alt="Cliente" 
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h4 className="font-medium">Marcos Oliveira</h4>
              <p className="text-sm text-gray-600">COO, Tech Solutions</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestimonialsTab;
