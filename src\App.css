
#root {
  max-width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: left;
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}

/* Purple and blue color utility classes */
.bg-purple-gradient {
  background: linear-gradient(135deg, #7E69AB 0%, #9b87f5 100%);
}

.bg-blue-purple-gradient {
  background: linear-gradient(90deg, #33C3F0 0%, #7E69AB 100%);
}

.text-purple {
  color: #7E69AB;
}

.text-blue {
  color: #33C3F0;
}

.border-purple {
  border-color: #7E69AB;
}

.border-blue {
  border-color: #33C3F0;
}

/* Board of Innovation inspired styles */
.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.diagonal-gradient {
  background: linear-gradient(135deg, #7E69AB 0%, #33C3F0 100%);
}

.scale-on-hover {
  transition: transform 0.3s ease;
}

.scale-on-hover:hover {
  transform: scale(1.05);
}

/* Custom button styles */
.btn-hover-effect {
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.btn-hover-effect:after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  z-index: -1;
  transform: scaleX(0);
  transform-origin: right;
  transition: transform 0.4s ease;
}

.btn-hover-effect:hover:after {
  transform: scaleX(1);
  transform-origin: left;
}
