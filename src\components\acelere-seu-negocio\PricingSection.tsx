
import { Check<PERSON>ir<PERSON>, ArrowR<PERSON> } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from "react-router-dom";

const PricingSection = () => {
  const navigate = useNavigate();

  const handleEnrollment = () => {
    navigate("/inscricao-programa");
  };

  const includedFeatures = [
    "Acesso completo ao programa de 3 meses",
    "24 aulas online com especialistas",
    "12 workshops práticos",
    "6 sessões de mentoria individual",
    "Diagnóstico completo do seu negócio",
    "Networking com investidores",
    "Participação no Demo Day",
    "Certificado de conclusão",
    "Acesso vitalício à comunidade"
  ];

  return (
    <section id="inscricao" className="py-20 bg-gray-900 text-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-white mb-6">
            Invista no Futuro do Seu Negócio
          </h2>
          <p className="text-xl text-white/80 max-w-3xl mx-auto">
            Vagas limitadas para garantir atendimento personalizado a cada empresa participante
          </p>
        </div>

        <div className="max-w-4xl mx-auto">
          <Card className="bg-white text-gray-900 rounded-2xl overflow-hidden shadow-2xl">
            <div className="grid grid-cols-1 md:grid-cols-2">
              <div className="p-8 md:p-12 bg-brand text-white">
                <h3 className="text-2xl font-bold mb-4">Programa Completo<br/>Acelere seu Negócio</h3>
                <div className="mb-6">
                  <span className="text-5xl font-bold">R$ 15.000</span>
                  <span className="text-white/80"> / empresa</span>
                </div>
                <p className="text-white/90 mb-6">
                  Investimento único para transformar seu negócio e alcançar novos patamares.
                </p>
                <p className="text-sm mb-4">Opções de pagamento:</p>
                <ul className="space-y-2 mb-8">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-3 flex-shrink-0" />
                    <span>À vista com 10% de desconto</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-3 flex-shrink-0" />
                    <span>Em até 12x no cartão</span>
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-3 flex-shrink-0" />
                    <span>Consulte condições especiais para startups em estágio inicial</span>
                  </li>
                </ul>
              </div>
              <div className="p-8 md:p-12">
                <h3 className="text-2xl font-bold mb-6">O que está incluído:</h3>
                <ul className="space-y-4">
                  {includedFeatures.map((item, idx) => (
                    <li key={idx} className="flex items-start">
                      <CheckCircle className="h-5 w-5 text-brand mt-0.5 mr-3 flex-shrink-0" />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
                <div className="mt-8">
                  <Button 
                    onClick={handleEnrollment}
                    className="w-full bg-brand hover:bg-brand-secondary text-white px-8 py-6 text-lg font-medium"
                  >
                    Inscreva-se Agora
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                  <p className="text-center text-sm text-gray-500 mt-4">
                    Vagas limitadas. Processo seletivo sujeito à aprovação.
                  </p>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default PricingSection;
