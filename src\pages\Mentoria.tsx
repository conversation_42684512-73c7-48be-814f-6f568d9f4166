import { useEffect, useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { 
  GraduationCap, 
  Briefcase, 
  MessageCircle, 
  Users, 
  Star, 
  Calendar, 
  ChevronRight,
  Filter,
  Search
} from "lucide-react";
import { 
  Card, 
  CardContent, 
  CardFooter, 
  CardHeader 
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

// Mentor data
const featuredMentors = [
  {
    id: 1,
    name: "<PERSON>",
    role: "CEO & Founder",
    company: "TechSpark",
    expertise: ["Startup", "Liderança", "Inovação"],
    image: "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mnx8YnVzaW5lc3N3b21hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.9,
    sessions: 48,
    bio: "Empreendedora serial com mais de 15 anos de experiência em startups de tecnologia.",
    availability: ["Segunda", "Quarta", "Sexta"],
    category: "Empreendedorismo"
  },
  {
    id: 2,
    name: "Carlos Mendes",
    role: "CTO",
    company: "Inovation Hub",
    expertise: ["IA", "Desenvolvimento", "Produto"],
    image: "https://images.unsplash.com/photo-1560250097-0b93528c311a?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8M3x8YnVzaW5lc3NtYW58ZW58MHx8MHx8fDA%3D",
    rating: 4.8,
    sessions: 36,
    bio: "Especialista em inteligência artificial e desenvolvimento de produtos digitais.",
    availability: ["Terça", "Quinta", "Sábado"],
    category: "Tecnologia"
  },
  {
    id: 3,
    name: "Júlia Santos",
    role: "Investment Director",
    company: "Venture Capital Partners",
    expertise: ["Investimento", "Finanças", "Negociação"],
    image: "https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTZ8fGJ1c2luZXNzd29tYW58ZW58MHx8MHx8fDA%3D",
    rating: 4.9,
    sessions: 52,
    bio: "Diretora de investimentos com portfólio especializado em startups early-stage.",
    availability: ["Segunda", "Quarta", "Sexta"],
    category: "Investimento"
  },
  {
    id: 4,
    name: "Pedro Oliveira",
    role: "Marketing Director",
    company: "Growth Solutions",
    expertise: ["Marketing", "Growth", "Branding"],
    image: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8YnVzaW5lc3NtYW58ZW58MHx8MHx8fDA%3D",
    rating: 4.7,
    sessions: 29,
    bio: "Especialista em estratégias de crescimento e marketing digital para startups.",
    availability: ["Terça", "Quinta", "Sábado"],
    category: "Marketing"
  }
];

const allMentors = [
  ...featuredMentors,
  {
    id: 5,
    name: "Amanda Costa",
    role: "COO",
    company: "ScaleUp Tech",
    expertise: ["Operações", "Processos", "Gestão"],
    image: "https://images.unsplash.com/photo-1551836022-d5d88e9218df?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8N3x8YnVzaW5lc3N3b21hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.6,
    sessions: 22,
    bio: "Especialista em operações e escalabilidade de startups.",
    availability: ["Segunda", "Quarta"],
    category: "Operações"
  },
  {
    id: 6,
    name: "Ricardo Neves",
    role: "Head of Sales",
    company: "SaaS Growth",
    expertise: ["Vendas", "B2B", "Negociação"],
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTJ8fG1hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.8,
    sessions: 31,
    bio: "Especialista em estratégias de vendas B2B para empresas SaaS.",
    availability: ["Terça", "Quinta"],
    category: "Vendas"
  },
  {
    id: 7,
    name: "Luisa Fernandes",
    role: "UX/UI Director",
    company: "Design Hub",
    expertise: ["Design", "UX/UI", "Produto"],
    image: "https://images.unsplash.com/photo-1580894732444-8ecded7900cd?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8Mjh8fGJ1c2luZXNzd29tYW58ZW58MHx8MHx8fDA%3D",
    rating: 4.9,
    sessions: 45,
    bio: "Diretora de experiência do usuário focada em produtos digitais inovadores.",
    availability: ["Quarta", "Sexta"],
    category: "Design"
  },
  {
    id: 8,
    name: "Gabriel Almeida",
    role: "CFO",
    company: "Financial Solutions",
    expertise: ["Finanças", "Investimento", "Valuation"],
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTR8fG1hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.7,
    sessions: 27,
    bio: "Especialista em finanças para startups em diferentes estágios de crescimento.",
    availability: ["Segunda", "Sexta"],
    category: "Finanças"
  },
  {
    id: 9,
    name: "Camila Soares",
    role: "Head of People",
    company: "Talent Hub",
    expertise: ["RH", "Cultura", "Liderança"],
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjZ8fHdvbWFufGVufDB8fDB8fHww",
    rating: 4.8,
    sessions: 33,
    bio: "Especialista em gestão de pessoas e cultura organizacional em empresas de tecnologia.",
    availability: ["Terça", "Quinta"],
    category: "RH"
  },
  {
    id: 10,
    name: "Eduardo Santos",
    role: "Tech Lead",
    company: "Dev Solutions",
    expertise: ["Tecnologia", "Arquitetura", "Desenvolvimento"],
    image: "https://images.unsplash.com/photo-1548449112-96a38a643324?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MTh8fG1hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.9,
    sessions: 40,
    bio: "Especialista em desenvolvimento de software e arquitetura de sistemas.",
    availability: ["Segunda", "Quarta", "Sexta"],
    category: "Tecnologia"
  },
  {
    id: 11,
    name: "Mariana Costa",
    role: "Strategic Advisor",
    company: "Growth Consultoria",
    expertise: ["Estratégia", "Consultoria", "Internacionalização"],
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MzB8fHdvbWFufGVufDB8fDB8fHww",
    rating: 4.7,
    sessions: 25,
    bio: "Consultora estratégica com experiência em expansão internacional de startups.",
    availability: ["Terça", "Quinta"],
    category: "Estratégia"
  },
  {
    id: 12,
    name: "Rodrigo Lima",
    role: "Product Manager",
    company: "Product Labs",
    expertise: ["Produto", "Inovação", "Metodologias Ágeis"],
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=400&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8MjB8fG1hbnxlbnwwfHwwfHx8MA%3D%3D",
    rating: 4.8,
    sessions: 38,
    bio: "Gerente de produto especializado em metodologias ágeis e inovação.",
    availability: ["Segunda", "Quarta", "Sexta"],
    category: "Produto"
  }
];

// Categories for filtering
const mentorCategories = [
  "Todos",
  "Empreendedorismo",
  "Tecnologia",
  "Investimento",
  "Marketing",
  "Operações",
  "Vendas",
  "Design",
  "Finanças",
  "RH",
  "Estratégia",
  "Produto"
];

const Mentoria = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("Todos");
  
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Mentoria | ATAC Academy";
  }, []);

  // Filter mentors based on search and category
  const filteredMentors = allMentors.filter(mentor => {
    const matchesSearch = mentor.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          mentor.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          mentor.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          mentor.expertise.some(skill => skill.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesCategory = selectedCategory === "Todos" || mentor.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-20">
        {/* Hero Section */}
        <section className="bg-gradient-to-r from-brand-light to-white py-16 mb-12">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h1 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                Programa de <span className="text-brand">Mentoria</span> Especializada
              </h1>
              <p className="text-xl text-gray-700 mb-8">
                Conecte-se com empreendedores experientes e receba orientação personalizada para superar desafios e escalar seu negócio.
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button className="bg-brand hover:bg-brand-secondary">
                  Agendar Mentoria
                </Button>
                <Button className="bg-brand hover:bg-brand-secondary">
                  Tornar-se Mentor
                </Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* Benefits Section */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Por que participar?
              </h2>
              <p className="text-lg text-gray-700">
                Nosso programa de mentoria proporciona orientação personalizada para ajudar você a superar desafios e acelerar o crescimento do seu negócio.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="feature-box">
                <div className="bg-brand-light rounded-full w-16 h-16 flex items-center justify-center mb-4">
                  <GraduationCap className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-2">Conhecimento Especializado</h3>
                <p className="text-gray-700">
                  Acesso a mentores com experiência comprovada em diversas áreas de negócios e tecnologia.
                </p>
              </div>
              
              <div className="feature-box">
                <div className="bg-brand-light rounded-full w-16 h-16 flex items-center justify-center mb-4">
                  <Briefcase className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-2">Rede de Contatos</h3>
                <p className="text-gray-700">
                  Amplie seu networking com outros empreendedores e profissionais do mercado.
                </p>
              </div>
              
              <div className="feature-box">
                <div className="bg-brand-light rounded-full w-16 h-16 flex items-center justify-center mb-4">
                  <MessageCircle className="h-8 w-8 text-brand" />
                </div>
                <h3 className="text-xl font-bold mb-2">Feedback Personalizado</h3>
                <p className="text-gray-700">
                  Receba insights valiosos sobre seu modelo de negócio, produto e estratégia de crescimento.
                </p>
              </div>
            </div>
          </div>
        </section>
        
        {/* Featured Mentors */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Mentores Destacados
              </h2>
              <p className="text-lg text-gray-700">
                Conheça alguns dos nossos mentores especializados em diferentes áreas de negócios
              </p>
            </div>
            
            <div className="mx-auto max-w-7xl px-4">
              <Carousel
                opts={{
                  align: "start",
                  loop: true,
                }}
                className="w-full"
              >
                <CarouselContent>
                  {featuredMentors.map((mentor) => (
                    <CarouselItem key={mentor.id} className="md:basis-1/2 lg:basis-1/3 pl-4">
                      <div className="p-1">
                        <Card className="overflow-hidden hover:shadow-lg transition-all duration-300">
                          <div className="h-48 overflow-hidden">
                            <img 
                              src={mentor.image} 
                              alt={mentor.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <CardHeader className="p-6 pb-2">
                            <div className="flex items-center gap-4">
                              <Avatar className="h-12 w-12 border-2 border-brand mt-[-40px] bg-white">
                                <AvatarImage src={mentor.image} alt={mentor.name} />
                                <AvatarFallback>{mentor.name.slice(0, 2)}</AvatarFallback>
                              </Avatar>
                              <div>
                                <h3 className="text-xl font-bold">{mentor.name}</h3>
                                <p className="text-gray-600">{mentor.role} - {mentor.company}</p>
                              </div>
                            </div>
                          </CardHeader>
                          <CardContent className="p-6 pt-3">
                            <div className="flex items-center gap-2 mb-2">
                              <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                              <span className="text-sm font-medium">{mentor.rating}</span>
                              <span className="text-sm text-gray-500">({mentor.sessions} sessões)</span>
                            </div>
                            <p className="text-gray-700 mb-3 line-clamp-2">{mentor.bio}</p>
                            <div className="flex flex-wrap gap-2 mb-3">
                              {mentor.expertise.map((skill, index) => (
                                <span 
                                  key={index} 
                                  className="bg-brand-light text-brand text-xs font-medium px-2 py-1 rounded-full"
                                >
                                  {skill}
                                </span>
                              ))}
                            </div>
                          </CardContent>
                          <CardFooter className="p-6 pt-0">
                            <Link to={`/mentor/${mentor.id}`} className="w-full">
                              <Button className="w-full bg-brand hover:bg-brand-secondary">
                                Ver Perfil
                              </Button>
                            </Link>
                          </CardFooter>
                        </Card>
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <div className="flex justify-center mt-8">
                  <CarouselPrevious className="relative static transform-none translate-y-0 left-0 mr-2" />
                  <CarouselNext className="relative static transform-none translate-y-0 right-0 ml-2" />
                </div>
              </Carousel>
            </div>
          </div>
        </section>
        
        {/* All Mentors */}
        <section className="py-16">
          <div className="container mx-auto px-4">
            <div className="text-center max-w-3xl mx-auto mb-12">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Encontre o Mentor Ideal
              </h2>
              <p className="text-lg text-gray-700">
                Busque entre nossa rede de mentores especializados e encontre o profissional perfeito para ajudar seu negócio
              </p>
            </div>
            
            {/* Search and Filter */}
            <div className="mb-10 max-w-4xl mx-auto">
              <div className="flex flex-col md:flex-row gap-4 mb-8">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <input 
                    type="text" 
                    placeholder="Buscar por nome, especialidade ou empresa" 
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="relative w-full md:w-64">
                  <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                  <select 
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent appearance-none bg-white"
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                  >
                    {mentorCategories.map((category) => (
                      <option key={category} value={category}>{category}</option>
                    ))}
                  </select>
                  <ChevronRight className="absolute right-3 top-1/2 transform -translate-y-1/2 rotate-90 text-gray-400" size={20} />
                </div>
              </div>
              
              {/* Categories Pills */}
              <div className="flex flex-wrap gap-2 mb-4">
                {mentorCategories.map((category) => (
                  <button
                    key={category}
                    className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                      selectedCategory === category 
                        ? 'bg-brand text-white' 
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => setSelectedCategory(category)}
                  >
                    {category}
                  </button>
                ))}
              </div>
            </div>
            
            {/* Mentors Grid */}
            {filteredMentors.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {filteredMentors.map((mentor) => (
                  <Card key={mentor.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 h-full">
                    <div className="h-36 sm:h-48 overflow-hidden">
                      <img 
                        src={mentor.image} 
                        alt={mentor.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <CardHeader className="p-4 pb-2">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12 border-2 border-brand mt-[-36px] bg-white">
                          <AvatarImage src={mentor.image} alt={mentor.name} />
                          <AvatarFallback>{mentor.name.slice(0, 2)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-lg font-bold">{mentor.name}</h3>
                          <p className="text-sm text-gray-600">{mentor.role}</p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="p-4 pt-2">
                      <div className="flex items-center gap-2 mb-2">
                        <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                        <span className="text-sm font-medium">{mentor.rating}</span>
                        <span className="text-xs text-gray-500">({mentor.sessions} sessões)</span>
                      </div>
                      <p className="text-gray-700 text-sm mb-3 line-clamp-2">{mentor.bio}</p>
                      <div className="flex flex-wrap gap-1 mb-3">
                        {mentor.expertise.slice(0, 3).map((skill, index) => (
                          <span 
                            key={index} 
                            className="bg-brand-light text-brand text-xs font-medium px-2 py-1 rounded-full"
                          >
                            {skill}
                          </span>
                        ))}
                      </div>
                    </CardContent>
                    <CardFooter className="p-4 pt-0 mt-auto">
                      <Link to={`/mentor/${mentor.id}`} className="w-full">
                        <Button className="w-full bg-brand hover:bg-brand-secondary">
                          Ver Perfil
                        </Button>
                      </Link>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="text-center py-16">
                <div className="mb-4">
                  <Users className="h-16 w-16 text-gray-300 mx-auto" />
                </div>
                <h3 className="text-xl font-bold mb-2">Nenhum mentor encontrado</h3>
                <p className="text-gray-600">
                  Tente ajustar seus filtros ou termos de busca para encontrar mentores disponíveis.
                </p>
              </div>
            )}
          </div>
        </section>
        
        {/* Call to Action */}
        <section className="py-16 bg-gradient-to-r from-brand to-brand-secondary text-white">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
                Pronto para acelerar seu negócio?
              </h2>
              <p className="text-xl mb-8 opacity-90">
                Agende uma mentoria especializada e dê o próximo passo no crescimento da sua empresa
              </p>
              <div className="flex flex-col sm:flex-row justify-center gap-4">
                <Button className="bg-white text-brand hover:bg-gray-100">
                  Agendar Mentoria
                </Button>
                <Button className="bg-white text-brand hover:bg-gray-100">
                  Conhecer Planos
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Mentoria;
