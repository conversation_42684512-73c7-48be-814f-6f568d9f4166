
import { <PERSON> } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { ArrowRight, MapPin } from "lucide-react";

// Innovation ecosystems data
const innovationEcosystems = [
  {
    id: 1,
    name: "Vale do Silício",
    location: "Estados Unidos",
    description: "Berço das maiores empresas de tecnologia do mundo e centro global de inovação e capital de risco",
    image: "https://images.unsplash.com/photo-1614850715649-c8c472be6f62?q=80&w=2940&auto=format&fit=crop",
    highlights: ["Big Techs", "Venture Capital", "Startups Unicórnios"]
  },
  {
    id: 2,
    name: "Shenzhen",
    location: "China",
    description: "Capital mundial do hardware e manufatura, líder em inovação em eletrônicos e prototipagem rápida",
    image: "https://images.unsplash.com/photo-1522536421511-14c9073df899?q=80&w=2940&auto=format&fit=crop",
    highlights: ["Hardware", "Manufatura", "IoT"]
  },
  {
    id: 3,
    name: "Tel Aviv",
    location: "Israel",
    description: "Ecossistema de deep tech com foco em cibersegurança, conhecida como a Startup Nation",
    image: "https://images.unsplash.com/photo-1544971587-b842c27f8e1b?q=80&w=2940&auto=format&fit=crop",
    highlights: ["Cibersegurança", "Defesa", "DeepTech"]
  },
  {
    id: 4,
    name: "Berlim",
    location: "Alemanha",
    description: "Hub europeu de criatividade e tecnologia, com foco em sustentabilidade e economia circular",
    image: "https://images.unsplash.com/photo-1560969184-10fe8719e047?q=80&w=2940&auto=format&fit=crop",
    highlights: ["Sustentabilidade", "Economia Circular", "Creative Tech"]
  },
  {
    id: 5,
    name: "Singapura",
    location: "Sudeste Asiático",
    description: "Centro financeiro e de inovação com forte apoio governamental e infraestrutura de classe mundial",
    image: "https://images.unsplash.com/photo-1565967511849-76a60a516170?q=80&w=2940&auto=format&fit=crop",
    highlights: ["Fintech", "Smart City", "Políticas Públicas"]
  },
  {
    id: 6,
    name: "Tóquio",
    location: "Japão",
    description: "Liderança em robótica, inteligência artificial e inovação em hardware com empresas centenárias",
    image: "https://images.unsplash.com/photo-1536098561742-ca998e48cbcc?q=80&w=2936&auto=format&fit=crop",
    highlights: ["Robótica", "Automação", "Corporações"]
  }
];

const InnovationEcosystems = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Explore <span className="text-brand-accent">Ecossistemas Inovadores</span> no mundo
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Conheça os principais hubs de inovação global e descubra como cada região desenvolve 
            seu próprio modelo de sucesso em tecnologia e empreendedorismo
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {innovationEcosystems.map((ecosystem) => (
            <div key={ecosystem.id} className="bg-white rounded-xl overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 hover:-translate-y-1">
              <div className="relative h-48 overflow-hidden">
                <img 
                  src={ecosystem.image} 
                  alt={ecosystem.name}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent" />
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <div className="flex items-center text-white mb-1">
                    <MapPin className="h-4 w-4 mr-1" />
                    <span className="text-sm font-medium">{ecosystem.location}</span>
                  </div>
                  <h3 className="font-montserrat text-xl font-semibold text-white">
                    {ecosystem.name}
                  </h3>
                </div>
              </div>
              
              <div className="p-6">
                <p className="text-gray-700 mb-4">{ecosystem.description}</p>
                
                <div className="mt-4">
                  <h4 className="font-semibold text-gray-900 mb-2">Destaques:</h4>
                  <div className="flex flex-wrap gap-2">
                    {ecosystem.highlights.map((highlight, index) => (
                      <span 
                        key={index} 
                        className="bg-gray-100 text-gray-800 text-sm font-medium px-3 py-1 rounded-full"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <Link to="/contato">
            <Button size="lg" className="bg-brand hover:bg-brand-secondary">
              Saiba mais sobre nossas imersões
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default InnovationEcosystems;
