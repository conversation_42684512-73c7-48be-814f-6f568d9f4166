
import { CheckCircle2 } from "lucide-react";
import EnrollmentSidebar from "../EnrollmentSidebar";

interface OverviewTabProps {
  course: any;
  onEnrollmentClick: () => void;
}

const OverviewTab = ({ course, onEnrollmentClick }: OverviewTabProps) => {
  return (
    <div className="container mx-auto px-4">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
        <div className="lg:col-span-2">
          <div className="prose prose-lg max-w-none">
            <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-6">Sobre o Programa</h2>
            <p className="text-lg text-gray-700 mb-6">{course.longDescription}</p>
            
            <h3 className="font-montserrat text-2xl font-bold text-gray-900 mt-12 mb-6">Quem Deve Participar</h3>
            <ul className="space-y-3">
              {course.targetAudience.map((item, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle2 className="h-6 w-6 text-brand-accent mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-lg">{item}</span>
                </li>
              ))}
            </ul>
            
            <h3 className="font-montserrat text-2xl font-bold text-gray-900 mt-12 mb-6">O Que Você Vai Aprender</h3>
            <ul className="space-y-3">
              {course.benefits.map((benefit, index) => (
                <li key={index} className="flex items-start">
                  <CheckCircle2 className="h-6 w-6 text-brand-accent mr-3 flex-shrink-0 mt-0.5" />
                  <span className="text-lg">{benefit}</span>
                </li>
              ))}
            </ul>
          </div>
          
          {/* Testimonials */}
          <div className="mt-16">
            <h3 className="font-montserrat text-2xl font-bold text-gray-900 mb-8">O Que Dizem Nossos Alunos</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {course.testimonials.map((testimonial, index) => (
                <div key={index} className="bg-gray-50 p-6 rounded-lg border border-gray-100">
                  <div className="flex items-center mb-4">
                    <img 
                      src={testimonial.image} 
                      alt={testimonial.name}
                      className="w-14 h-14 rounded-full object-cover mr-4"
                    />
                    <div>
                      <h4 className="font-semibold text-lg">{testimonial.name}</h4>
                      <p className="text-gray-600">{testimonial.role}</p>
                    </div>
                  </div>
                  <p className="text-gray-700">"{testimonial.text}"</p>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        {/* Sidebar */}
        <EnrollmentSidebar course={course} onEnrollmentClick={onEnrollmentClick} />
      </div>
    </div>
  );
};

export default OverviewTab;
