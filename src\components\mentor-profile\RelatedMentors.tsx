
import { <PERSON> } from "react-router-dom";
import { <PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON> } from "@/data/mentors";

interface RelatedMentorsProps {
  currentMentorId: number;
  mentors: <PERSON><PERSON>[];
  expertise: string[];
}

const RelatedMentors = ({ currentMentorId, mentors, expertise }: RelatedMentorsProps) => {
  // Filter mentors related to the current mentor's expertise
  const relatedMentors = mentors
    .filter((m) => 
      m.id !== currentMentorId && 
      m.expertise.some((e: string) => expertise.includes(e))
    )
    .slice(0, 4);

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <h2 className="text-2xl font-bold mb-8">Outros Mentores Relacionados</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {relatedMentors.map((relatedMentor) => (
            <div 
              key={relatedMentor.id} 
              className="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow border"
            >
              <div className="h-40 overflow-hidden">
                <img 
                  src={relatedMentor.image} 
                  alt={relatedMentor.name}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="p-4">
                <h3 className="font-bold text-lg mb-1">{relatedMentor.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{relatedMentor.role} - {relatedMentor.company}</p>
                <div className="flex flex-wrap gap-1 mb-3">
                  {relatedMentor.expertise.slice(0, 2).map((skill: string, idx: number) => (
                    <span 
                      key={idx} 
                      className="bg-brand-light text-brand text-xs font-medium px-2 py-1 rounded-full"
                    >
                      {skill}
                    </span>
                  ))}
                </div>
                <div className="flex items-center gap-2 mb-3">
                  <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
                  <span className="font-medium">{relatedMentor.rating}</span>
                  <span className="text-xs text-gray-500">({relatedMentor.sessions} sessões)</span>
                </div>
                <Link to={`/mentor/${relatedMentor.id}`}>
                  <Button className="w-full bg-brand hover:bg-brand-secondary">
                    Ver Perfil
                  </Button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default RelatedMentors;
