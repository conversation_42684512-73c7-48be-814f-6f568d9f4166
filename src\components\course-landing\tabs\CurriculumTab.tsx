
import { BookOpen, Users } from "lucide-react";
import { Button } from "@/components/ui/button";

interface CurriculumTabProps {
  course: any;
  onEnrollmentClick: () => void;
}

const CurriculumTab = ({ course, onEnrollmentClick }: CurriculumTabProps) => {
  return (
    <div className="container mx-auto px-4">
      <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-10">
        Grade Curricular
      </h2>
      
      <div className="space-y-8">
        {course.modules.map((module, index) => (
          <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="bg-gray-50 p-6">
              <h3 className="font-montserrat text-xl font-bold">
                Mó<PERSON>lo {index + 1}: {module.title}
              </h3>
            </div>
            <div className="divide-y divide-gray-200">
              {module.lessons.map((lesson, lessonIndex) => (
                <div key={lessonIndex} className="p-6 flex justify-between items-center">
                  <div className="flex items-center">
                    {lesson.includes("Workshop") ? (
                      <div className="w-10 h-10 rounded-full bg-brand-accent/10 flex items-center justify-center mr-4">
                        <Users className="h-5 w-5 text-brand-accent" />
                      </div>
                    ) : (
                      <div className="w-10 h-10 rounded-full bg-brand/10 flex items-center justify-center mr-4">
                        <BookOpen className="h-5 w-5 text-brand" />
                      </div>
                    )}
                    <span className="text-lg">{lesson}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-12 text-center">
        <Button 
          className="bg-brand-accent hover:bg-brand-accent/90 text-white px-8 py-6 text-xl"
          onClick={onEnrollmentClick}
        >
          Garanta Sua Vaga
        </Button>
      </div>
    </div>
  );
};

export default CurriculumTab;
