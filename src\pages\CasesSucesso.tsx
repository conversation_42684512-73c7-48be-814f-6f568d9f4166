
import { useEffect, useState } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Trophy, Target, Lightbulb, ArrowRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";

// Success case data
const successCases = [
  {
    category: "fintech",
    title: "Banco + Fintech de Crédito",
    subtitle: "A Jornada",
    award: "Vencedor do Prêmio Inovação Financeira 2022",
    challenge: "Acelerar análise de crédito para PMEs",
    solution: "Integração de API de análise alternativa de crédito",
    result: "Redução de 70% no tempo de análise",
    description: "Um dos maiores bancos do Brasil enfrentava dificuldades em analisar crédito para pequenas empresas de forma eficiente. Através do programa Corporate Challenges, selecionamos uma fintech especializada em análise alternativa de dados.",
    steps: [
      "Identificamos que o problema real era a falta de dados estruturados sobre PMEs",
      "A startup adaptou seu algoritmo para integrar-se aos sistemas legados do banco",
      "Criamos um fast track de contratação e segurança para viabilizar o piloto em 3 semanas"
    ],
    logo: "/lovable-uploads/84acc76f-7f0c-4a07-9355-2e604b347140.png",
  },
  {
    category: "industria",
    title: "Indústria 4.0 + IoT Startup",
    subtitle: "Transformação Digital",
    award: "Case destacado no Fórum de Indústria 4.0",
    challenge: "Monitoramento remoto de equipamentos industriais",
    solution: "Plataforma IoT com sensores de baixo custo",
    result: "Redução de 45% em paradas não programadas",
    description: "Uma grande indústria farmacêutica precisava modernizar o monitoramento de seus equipamentos críticos para reduzir paradas não programadas. Através do programa de Open Innovation, conectamos com uma startup especializada em IoT industrial.",
    steps: [
      "Mapeamos os pontos críticos de falha nos equipamentos da linha de produção",
      "Desenvolvemos um conjunto de sensores customizados para as necessidades específicas",
      "Implementamos um dashboard em tempo real integrado com o sistema de manutenção existente"
    ],
    logo: "/lovable-uploads/765a19e3-9747-45ff-b27b-4ae988011be8.png",
  },
  {
    category: "varejo",
    title: "Varejo + AI Startup",
    subtitle: "Reinventando a Experiência",
    award: "Menção honrosa no Retail Innovation Awards",
    challenge: "Personalização da jornada do cliente em loja física",
    solution: "Sistema de recomendação baseado em visão computacional",
    result: "Aumento de 28% no ticket médio",
    description: "Uma rede de varejo com mais de 200 lojas buscava formas de personalizar a experiência do cliente no ambiente físico. Através de nosso hub de inovação, identificamos uma startup de inteligência artificial com solução aplicável.",
    steps: [
      "Realizamos um estudo comportamental dos consumidores nas lojas piloto",
      "Adaptamos os algoritmos de recomendação para operação em tempo real",
      "Integramos a solução com o programa de fidelidade existente, criando uma experiência omnichannel"
    ],
    logo: "/lovable-uploads/9a9271a7-33b9-4d34-8377-5898aef23e49.png",
  }
];

const CasesSucesso = () => {
  const [activeCategory, setActiveCategory] = useState("fintech");
  const activeCase = successCases.find(c => c.category === activeCategory) || successCases[0];

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Cases de Sucesso | ATAC";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <section className="pt-20 pb-16 md:pt-32 md:pb-24 bg-gradient-to-br from-blue-900 via-indigo-900 to-violet-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-montserrat text-4xl md:text-6xl font-bold mb-6 leading-tight">
              <span className="text-purple-400">Cases</span> de Sucesso
            </h1>
            <p className="text-xl opacity-90 mb-8 leading-relaxed">
              Exemplos reais de colaborações bem-sucedidas facilitadas pela ATAC
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4">
          <Tabs defaultValue="fintech" onValueChange={setActiveCategory} className="w-full">
            <TabsList className="w-full max-w-3xl mx-auto grid grid-cols-3 mb-16">
              <TabsTrigger 
                value="fintech" 
                className="py-6 data-[state=active]:bg-white data-[state=active]:shadow-md rounded-md"
              >
                Fintech
              </TabsTrigger>
              <TabsTrigger 
                value="industria" 
                className="py-6 data-[state=active]:bg-white data-[state=active]:shadow-md rounded-md"
              >
                Indústria 4.0
              </TabsTrigger>
              <TabsTrigger 
                value="varejo" 
                className="py-6 data-[state=active]:bg-white data-[state=active]:shadow-md rounded-md"
              >
                Varejo
              </TabsTrigger>
            </TabsList>

            {successCases.map((caseItem) => (
              <TabsContent key={caseItem.category} value={caseItem.category} className="mt-0">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start max-w-6xl mx-auto">
                  {/* Left Column */}
                  <div>
                    <h2 className="text-3xl font-bold text-gray-900 mb-4">{caseItem.title}</h2>
                    <div className="flex items-center mb-8">
                      <Trophy className="h-5 w-5 text-yellow-500 mr-2" />
                      <span className="text-gray-600 italic">{caseItem.award}</span>
                    </div>
                    
                    <div className="mb-6">
                      <div className="flex items-center mb-2">
                        <div className="bg-purple-100 text-purple-700 px-4 py-2 rounded-full text-sm font-medium">
                          Desafio
                        </div>
                      </div>
                      <p className="text-gray-800 font-medium ml-2 mt-2">{caseItem.challenge}</p>
                    </div>
                    
                    <div className="mb-6">
                      <div className="flex items-center mb-2">
                        <div className="bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium">
                          Solução
                        </div>
                      </div>
                      <p className="text-gray-800 font-medium ml-2 mt-2">{caseItem.solution}</p>
                    </div>
                    
                    <div className="mb-8">
                      <div className="flex items-center mb-2">
                        <div className="bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium">
                          Resultado
                        </div>
                      </div>
                      <p className="text-gray-800 font-medium ml-2 mt-2">{caseItem.result}</p>
                    </div>
                  </div>
                  
                  {/* Right Column */}
                  <div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-6">{caseItem.subtitle}</h3>
                    <p className="text-gray-700 mb-8 leading-relaxed">{caseItem.description}</p>
                    
                    <div className="space-y-6">
                      {caseItem.steps.map((step, index) => (
                        <div key={index} className="flex items-start">
                          <div className="flex-shrink-0 w-8 h-8 rounded-full bg-brand flex items-center justify-center text-white font-medium mr-4">
                            {index + 1}
                          </div>
                          <p className="text-gray-700">{step}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </TabsContent>
            ))}
          </Tabs>
        </div>
      </section>

      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Conheça mais sobre nossos programas de inovação</h2>
            <p className="text-gray-600 mb-8">
              Transforme seu negócio ou startup através de nossos programas especializados de inovação corporativa
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl">Corporate Challenges</CardTitle>
                <CardDescription>Resolva desafios reais do seu negócio</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mb-4">
                    <Target className="h-6 w-6 text-brand" />
                  </div>
                  <p className="text-gray-600 text-sm">
                    Conectamos grandes empresas com startups inovadoras para resolver desafios específicos de negócio.
                  </p>
                </div>
                <Link to="/corporate-challenges">
                  <Button variant="outline" className="w-full group">
                    Saiba mais 
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
            
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl">Innovation Hubs</CardTitle>
                <CardDescription>Crie seu centro de inovação</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="w-12 h-12 rounded-full bg-purple-100 flex items-center justify-center mb-4">
                    <Lightbulb className="h-6 w-6 text-brand" />
                  </div>
                  <p className="text-gray-600 text-sm">
                    Ajudamos empresas a estabelecer e operar hubs de inovação conectados ao ecossistema de startups.
                  </p>
                </div>
                <Link to="/innovation-hubs">
                  <Button variant="outline" className="w-full group">
                    Saiba mais
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
            
            <Card className="shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader className="pb-4">
                <CardTitle className="text-xl">Open Innovation</CardTitle>
                <CardDescription>Transforme sua cultura</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="mb-6">
                  <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-4">
                    <Lightbulb className="h-6 w-6 text-brand" />
                  </div>
                  <p className="text-gray-600 text-sm">
                    Programas completos de transformação cultural e capacitação em inovação aberta para sua empresa.
                  </p>
                </div>
                <Link to="/open-innovation">
                  <Button variant="outline" className="w-full group">
                    Saiba mais
                    <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Button>
                </Link>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-br from-brand/10 to-brand-accent/10">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl font-bold text-gray-900 mb-6">Quer ser o próximo case de sucesso?</h2>
            <p className="text-gray-600 mb-8">
              Entre em contato com nossa equipe e descubra como podemos ajudar seu negócio
              a alcançar novos patamares através da inovação aberta.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link to="/contato">
                <Button className="bg-brand hover:bg-brand-secondary">
                  Fale com um especialista
                </Button>
              </Link>
              <Link to="/open-innovation">
                <Button variant="outline">
                  Conheça nossos programas
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default CasesSucesso;
