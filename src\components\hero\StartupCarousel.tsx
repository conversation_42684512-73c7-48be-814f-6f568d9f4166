
import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from "@/components/ui/carousel";

// Startups aceleradas
const acceleratedStartups = [
  {
    name: "Company 1",
    logoUrl: "/lovable-uploads/b4470602-f52e-4e23-a35b-96d4f1d10cae.png"
  },
  {
    name: "Company 2",
    logoUrl: "/lovable-uploads/5c96ac48-de8a-4c35-938b-4512bbd9e0da.png"
  },
  {
    name: "Company 3",
    logoUrl: "/lovable-uploads/4399656e-a62f-4505-99f7-e31d8db79e90.png"
  },
  {
    name: "Company 4",
    logoUrl: "/lovable-uploads/404e5514-d5d1-42e3-aec7-7afe0659a109.png"
  },
  {
    name: "Company 5",
    logoUrl: "/lovable-uploads/df9529ed-ecac-4ab6-9e49-549a6c8ef697.png"
  }
];

interface StartupCarouselProps {
  showCompanies: boolean;
}

const StartupCarousel = ({ showCompanies }: StartupCarouselProps) => {
  if (!showCompanies) return null;

  return (
    <div className="mt-16">
      <h3 className="text-center text-lg text-gray-500 mb-6">Startups Aceleradas</h3>
      <Carousel 
        className="w-full" 
        opts={{
          align: 'start',
          loop: true,
        }}
      >
        <CarouselContent>
          {acceleratedStartups.map((startup, index) => (
            <CarouselItem key={index} className="md:basis-1/3 lg:basis-1/5">
              <div className="p-1">
                <div className="flex items-center justify-center p-4 bg-white border border-gray-100 rounded-lg shadow-sm">
                  <img 
                    src={startup.logoUrl} 
                    alt={`Logo ${startup.name}`} 
                    className="h-auto max-h-12"
                  />
                </div>
              </div>
            </CarouselItem>
          ))}
        </CarouselContent>
      </Carousel>
    </div>
  );
};

export default StartupCarousel;
