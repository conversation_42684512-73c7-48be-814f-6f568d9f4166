
import { Button } from "@/components/ui/button";
import { CheckCircle2, Zap, Users, Award } from "lucide-react";

interface EnrollmentSidebarProps {
  course: any;
  onEnrollmentClick: () => void;
}

const EnrollmentSidebar = ({ course, onEnrollmentClick }: EnrollmentSidebarProps) => {
  return (
    <div id="enrollment" className="lg:col-span-1">
      <div className="bg-white rounded-lg border border-gray-200 shadow-lg sticky top-36 overflow-hidden">
        <div className="bg-brand p-6 text-white">
          <div className="flex justify-between items-start mb-2">
            <div className="line-through text-white/70 text-lg">{course.price.full}</div>
            <div className="bg-brand-accent text-white text-sm font-bold py-1 px-3 rounded">20% OFF</div>
          </div>
          <div className="flex items-end mb-4">
            <div className="text-3xl font-bold">{course.price.discounted}</div>
            <div className="text-white/90 ml-2 mb-1">à vista</div>
          </div>
          <div className="text-lg text-white/90">ou {course.price.installments} sem juros</div>
        </div>
        
        <div className="p-6">
          <h4 className="font-medium text-lg mb-4">Próximas Turmas:</h4>
          
          <div className="space-y-4 mb-6">
            {course.nextClasses.map((classDate, index) => (
              <div key={index} className={`border rounded-md p-4 ${index === 0 ? 'border-brand bg-brand/5' : 'border-gray-200'}`}>
                <div className="flex justify-between items-center">
                  <div>
                    <div className="font-medium">{classDate.date}</div>
                    <div className="text-sm text-gray-600">Restam {classDate.spots} vagas</div>
                  </div>
                  {index === 0 && (
                    <div className="h-6 w-6 rounded-full bg-brand flex items-center justify-center">
                      <CheckCircle2 className="h-4 w-4 text-white" />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
          
          <Button 
            className="w-full bg-brand-accent hover:bg-brand-accent/90 text-white py-6 text-lg mb-4"
            onClick={onEnrollmentClick}
          >
            Inscreva-se Agora
          </Button>
          
          <p className="text-sm text-gray-600 text-center mb-6">
            Garantia de 7 dias ou seu dinheiro de volta
          </p>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <Zap className="h-5 w-5 text-brand-accent mr-3" />
              <span>Acesso imediato à plataforma</span>
            </div>
            <div className="flex items-center">
              <Users className="h-5 w-5 text-brand-accent mr-3" />
              <span>Mentorias personalizadas</span>
            </div>
            <div className="flex items-center">
              <Award className="h-5 w-5 text-brand-accent mr-3" />
              <span>Certificado de conclusão</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnrollmentSidebar;
