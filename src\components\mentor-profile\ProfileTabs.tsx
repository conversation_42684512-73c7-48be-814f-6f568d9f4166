
import { useState } from "react";
import AboutTab from "./tabs/AboutTab";
import ExperienceTab from "./tabs/ExperienceTab";
import TestimonialsTab from "./tabs/TestimonialsTab";
import { <PERSON>tor } from "@/data/mentors";

interface ProfileTabsProps {
  mentor: <PERSON><PERSON>;
  benefits: string[];
}

const ProfileTabs = ({ mentor, benefits }: ProfileTabsProps) => {
  const [activeTab, setActiveTab] = useState("sobre");

  return (
    <div>
      <div className="border-b mb-6">
        <div className="flex">
          <button 
            className={`px-4 py-3 font-medium text-sm border-b-2 ${
              activeTab === "sobre" 
                ? "border-brand text-brand" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("sobre")}
          >
            Sobre
          </button>
          <button 
            className={`px-4 py-3 font-medium text-sm border-b-2 ${
              activeTab === "experiencia" 
                ? "border-brand text-brand" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("experiencia")}
          >
            Experiência
          </button>
          <button 
            className={`px-4 py-3 font-medium text-sm border-b-2 ${
              activeTab === "depoimentos" 
                ? "border-brand text-brand" 
                : "border-transparent text-gray-500 hover:text-gray-700"
            }`}
            onClick={() => setActiveTab("depoimentos")}
          >
            Depoimentos
          </button>
        </div>
      </div>
      
      {activeTab === "sobre" && <AboutTab mentor={mentor} benefits={benefits} />}
      {activeTab === "experiencia" && <ExperienceTab mentor={mentor} />}
      {activeTab === "depoimentos" && <TestimonialsTab mentor={mentor} />}
    </div>
  );
};

export default ProfileTabs;
