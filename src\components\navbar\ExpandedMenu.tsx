
import { ArrowRight } from "lucide-react";
import { Link } from "react-router-dom";
import { menuData } from "./MenuData";

interface ExpandedMenuProps {
  onClose: () => void;
}

export const ExpandedMenu = ({ onClose }: ExpandedMenuProps) => {
  return (
    <div className="fixed top-16 left-0 right-0 z-40 bg-white shadow-lg border-t border-gray-200 overflow-y-auto max-h-[calc(100vh-64px)]">
      <div className="container mx-auto py-3 md:py-6">
        {/* Menu Sections */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4 md:gap-8">
          {menuData.map((section) => (
            <div key={section.key} className="flex flex-col">
              <h2 className="text-base font-bold text-brand-accent mb-2 md:mb-4">{section.title}</h2>
              <ul className="space-y-2 md:space-y-3">
                {section.links.map((link, idx) => (
                  <li key={idx}>
                    <Link 
                      to={link.url} 
                      className="flex items-center text-gray-700 hover:text-brand-accent text-sm"
                      onClick={onClose}
                    >
                      {link.icon || <ArrowRight className="mr-2 text-brand-accent" size={16} />}
                      {link.label}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
