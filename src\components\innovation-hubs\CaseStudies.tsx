
import { ArrowR<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

const caseStudies = [
  {
    id: 1,
    company: "Banco Digital Brasileiro",
    sector: "Fintech",
    hub: "Londres",
    image: "https://images.unsplash.com/photo-**********-824ae1b704d3?auto=format&fit=crop&w=1170&q=80",
    title: "Transformação Digital em Serviços Financeiros",
    description: "Um dos maiores bancos brasileiros participou de um programa imersivo no hub de Londres, resultando na implementação de novas tecnologias de open banking e no lançamento de três novos produtos digitais.",
    results: ["Lançamento de plataforma de open banking", "44% de aumento em aquisição digital", "Parceria com 2 fintechs britânicas"]
  },
  {
    id: 2,
    company: "Varejista Multicanal",
    sector: "Retail Tech",
    hub: "Shenzhen",
    image: "https://images.unsplash.com/photo-*************-08184874388e?auto=format&fit=crop&w=1198&q=80",
    title: "Revolução da Cadeia de Suprimentos",
    description: "Uma rede varejista brasileira desenvolveu um programa no hub de Shenzhen focado em automação e IoT, transformando completamente sua operação logística e sistemas de gerenciamento de inventário.",
    results: ["Redução de 30% em custos logísticos", "Implementação de sistema IoT de ponta a ponta", "Melhoria de 60% na precisão de inventário"]
  },
  {
    id: 3,
    company: "Agrotech Nacional",
    sector: "Agribusiness",
    hub: "Tel Aviv",
    image: "https://images.unsplash.com/photo-*************-9049fed747ef?auto=format&fit=crop&w=1192&q=80",
    title: "Agricultura de Precisão com IA",
    description: "Uma empresa do agronegócio brasileiro trabalhou com startups israelenses para desenvolver sistemas de agricultura de precisão baseados em inteligência artificial e análise de dados avançada.",
    results: ["Aumento de 28% na produtividade", "Economia de 40% no uso de água e fertilizantes", "Desenvolvimento de algoritmo proprietário de previsão de safra"]
  }
];

const CaseStudies = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Cases de <span className="text-indigo-700">Sucesso</span>
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Conheça os resultados obtidos por empresas brasileiras através dos nossos Innovation Hubs
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {caseStudies.map((caseStudy) => (
            <Card key={caseStudy.id} className="overflow-hidden bg-white border-none shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="h-56 overflow-hidden">
                <img 
                  src={caseStudy.image} 
                  alt={caseStudy.title}
                  className="w-full h-full object-cover transition-transform duration-500 hover:scale-105"
                />
              </div>
              <CardContent className="p-6">
                <div className="flex justify-between items-center mb-3">
                  <span className="bg-indigo-100 text-indigo-800 text-xs px-2 py-1 rounded-full">
                    {caseStudy.sector}
                  </span>
                  <span className="text-sm text-gray-500">
                    Hub: {caseStudy.hub}
                  </span>
                </div>
                <h3 className="font-montserrat text-xl font-semibold text-gray-900 mb-3">
                  {caseStudy.title}
                </h3>
                <p className="text-gray-600 text-sm mb-5">
                  {caseStudy.description}
                </p>
                
                <div className="mb-5">
                  <h4 className="font-semibold text-gray-900 mb-2 text-sm">Resultados:</h4>
                  <ul className="space-y-1">
                    {caseStudy.results.map((result, idx) => (
                      <li key={idx} className="text-sm text-gray-700 flex items-start">
                        <span className="text-indigo-600 mr-2">•</span>
                        {result}
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
        
        <div className="text-center mt-12">
          <Button variant="outline" className="border-indigo-600 text-indigo-600 hover:bg-indigo-50">
            Ver mais cases
            <ArrowRight className="ml-2 h-4 w-4" />
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CaseStudies;
