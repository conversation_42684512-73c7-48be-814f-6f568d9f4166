
import { Globe, Briefcase, Rocket } from "lucide-react";

const BenefitsSection = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Por que participar de uma <span className="text-brand">Learning Experience</span>?
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto">
            Nossas imersões internacionais são cuidadosamente projetadas para oferecer uma experiência transformadora e gerar resultados práticos para você e seu negócio.
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
            <div className="w-14 h-14 rounded-full bg-brand-light flex items-center justify-center mb-6">
              <Globe className="h-7 w-7 text-brand" />
            </div>
            <h3 className="font-montserrat text-xl font-semibold text-gray-900 mb-3">
              Conexões Globais
            </h3>
            <p className="text-gray-700">
              Construa uma rede internacional de contatos com empreendedores, investidores e executivos de grandes empresas.
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
            <div className="w-14 h-14 rounded-full bg-brand-accent/20 flex items-center justify-center mb-6">
              <Briefcase className="h-7 w-7 text-brand-accent" />
            </div>
            <h3 className="font-montserrat text-xl font-semibold text-gray-900 mb-3">
              Acesso Privilegiado
            </h3>
            <p className="text-gray-700">
              Visite as sedes de empresas líderes globais e participe de conversas exclusivas com seus fundadores e executivos.
            </p>
          </div>
          
          <div className="bg-white p-8 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
            <div className="w-14 h-14 rounded-full bg-amber-100 flex items-center justify-center mb-6">
              <Rocket className="h-7 w-7 text-amber-600" />
            </div>
            <h3 className="font-montserrat text-xl font-semibold text-gray-900 mb-3">
              Inspiração e Inovação
            </h3>
            <p className="text-gray-700">
              Expanda sua visão de negócios e descubra tendências emergentes que podem ser aplicadas à sua empresa.
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default BenefitsSection;
