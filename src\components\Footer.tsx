import { <PERSON> } from "react-router-dom";
import { Facebook, Instagram, Linkedin, Twitter, Youtube, Mail, Phone, MapPin, ExternalLink } from "lucide-react";

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-10">
          <div>
            <Link to="/" className="inline-block mb-6">
              <span className="font-montserrat font-bold text-2xl">
                <span className="text-white">ATAC</span>
              </span>
            </Link>
            <p className="text-gray-400 mb-6">
              Acelerando o sucesso de empreendedores através de educação, mentoria e acesso a investimentos.
            </p>
            <div className="flex space-x-4">
              <a href="https://facebook.com/atacpro" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="https://instagram.com/atacpro" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="https://linkedin.com/company/atacpro" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="https://twitter.com/atacpro" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="https://youtube.com/channel/atacpro" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                <Youtube className="h-5 w-5" />
              </a>
            </div>
          </div>
          
          <div>
            <h3 className="font-semibold text-lg mb-4">Links Rápidos</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/quem-somos" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Quem Somos</span>
                </Link>
              </li>
              <li>
                <Link to="/cursos" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Cursos</span>
                </Link>
              </li>
              <li>
                <Link to="/mentoria" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Mentoria</span>
                </Link>
              </li>
              {/* <li>
                <Link to="/diagnostic" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Diagnóstico</span>
                </Link>
              </li> */}
              <li>
                <Link to="/open-innovation" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Open Innovation</span>
                </Link>
              </li>
              {/* <li>
                <Link to="/learning-experience" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Learning Experience</span>
                </Link>
              </li> */}
              <li>
                <Link to="/partnerships" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Parceiros</span>
                </Link>
              </li>
            </ul>
          </div>
          
          {/* <div>
            <h3 className="font-semibold text-lg mb-4">Serviços</h3>
            <ul className="space-y-3">
              <li>
                <Link to="/diagnostic" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Diagnóstico de Valuation</span>
                </Link>
              </li>
              <li>
                <Link to="/maturity" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Assessment de Maturidade</span>
                </Link>
              </li>
              <li>
                <Link to="/mentoria" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Mentoria Especializada</span>
                </Link>
              </li>
              <li>
                <Link to="/cursos" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Cursos Práticos</span>
                </Link>
              </li>
              <li>
                <Link to="/open-innovation" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Open Innovation</span>
                </Link>
              </li>
              <li>
                <Link to="/investidores" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Conexão com Investidores</span>
                </Link>
              </li>
              <li>
                <Link to="/acelere-seu-negocio" className="text-gray-400 hover:text-white transition-colors flex items-center gap-1">
                  <span>Acelere Seu Negócio</span>
                </Link>
              </li>
            </ul>
          </div> */}
          
          <div>
            <h3 className="font-semibold text-lg mb-4">Contato</h3>
            <ul className="space-y-4">
              <li className="flex items-start">
                <MapPin className="h-5 w-5 text-brand-accent mr-3 mt-1 flex-shrink-0" />
                <a href="https://maps.google.com/?q=Av.+Paulista,+1000,+São+Paulo+-+SP" target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-white transition-colors">
                  Rua Estados Unidos, 411 - Jardim Paulista
                  São Paulo - SP, 01427-000
                </a>
              </li>
              <li className="flex items-center">
                <Phone className="h-5 w-5 text-brand-accent mr-3 flex-shrink-0" />
                <a href="tel:+551140028922" className="text-gray-400 hover:text-white transition-colors">
                  (11) 4002-8922
                </a>
              </li>
              <li className="flex items-center">
                <Mail className="h-5 w-5 text-brand-accent mr-3 flex-shrink-0" />
                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white transition-colors">
                  <EMAIL>
                </a>
              </li>
            </ul>
            
            <div className="mt-6">
              <h4 className="font-medium mb-2">Assine nossa newsletter</h4>
              <div className="flex">
                <input
                  type="email"
                  placeholder="Seu e-mail"
                  className="bg-gray-800 border border-gray-700 px-4 py-2 rounded-l-md focus:outline-none focus:ring-1 focus:ring-brand-accent w-full"
                />
                <button className="bg-brand-accent hover:bg-orange-600 text-white px-4 py-2 rounded-r-md transition-colors">
                  Enviar
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-500 text-sm">
          <p>&copy; {currentYear} ATAC. Todos os direitos reservados.</p>
          <div className="mt-2 flex justify-center space-x-6">
            <Link to="/termos" className="hover:text-white transition-colors">Termos de Uso</Link>
            <Link to="/privacidade" className="hover:text-white transition-colors">Política de Privacidade</Link>
            <Link to="/cookies" className="hover:text-white transition-colors">Política de Cookies</Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
