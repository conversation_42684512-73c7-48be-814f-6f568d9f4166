
import { useRef } from "react";
import { Input } from "@/components/ui/input";
import { X } from "lucide-react";

interface SearchBarProps {
  isOpen: boolean;
  onClose: () => void;
  isMobile: boolean;
}

export const SearchBar = ({ isOpen, onClose, isMobile }: SearchBarProps) => {
  const searchInputRef = useRef<HTMLInputElement>(null);

  if (!isOpen) return null;

  if (isMobile) {
    return (
      <div className="fixed top-16 left-0 right-0 z-40 bg-white py-4 px-4 border-t border-gray-200 shadow-md">
        <div className="relative flex items-center">
          <Input
            ref={searchInputRef}
            type="text" 
            placeholder="PESQUISAR..." 
            className="bg-gray-200 rounded-full py-1.5 pl-3 pr-8 w-full h-8 focus:outline-none text-xs"
            onBlur={() => {
              setTimeout(() => onClose(), 200);
            }}
            onKeyDown={(e) => {
              if (e.key === 'Escape') {
                onClose();
              }
            }}
            autoFocus
          />
          <button className="absolute right-2" onClick={onClose}>
            <X size={14} className="text-gray-600" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative flex items-center">
      <Input
        ref={searchInputRef}
        type="text" 
        placeholder="PESQUISAR..." 
        className="bg-gray-200 rounded-full py-1.5 pl-3 pr-8 w-56 h-8 focus:outline-none text-xs"
        onBlur={() => {
          setTimeout(() => onClose(), 200);
        }}
        onKeyDown={(e) => {
          if (e.key === 'Escape') {
            onClose();
          }
        }}
        autoFocus
      />
      <button className="absolute right-2" onClick={onClose}>
        <X size={14} className="text-gray-600" />
      </button>
    </div>
  );
};
