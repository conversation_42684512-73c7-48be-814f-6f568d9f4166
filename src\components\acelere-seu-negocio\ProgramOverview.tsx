
import { CheckCircle } from "lucide-react";

const ProgramOverview = () => {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-12 items-center">
          <div className="md:w-1/2">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
              Transforme sua startup em um<br />
              <span className="text-brand-accent">negócio escalável</span>
            </h2>
            <p className="text-lg text-gray-700 mb-6">
              O programa "Acelere seu Negócio" foi desenvolvido para empreendedores que já possuem um produto validado no mercado e estão prontos para escalar suas operações de forma estruturada e sustentável.
            </p>
            <p className="text-lg text-gray-700 mb-6">
              Durante 3 meses intensivos, você terá acesso a mentorias individuais, workshops práticos e conteúdo exclusivo para impulsionar todos os aspectos do seu negócio.
            </p>
            <div className="space-y-4 mb-8">
              {[
                "Estruture seu modelo de negócio para escala",
                "Otimize processos e métricas de crescimento",
                "Prepare-se para captação de investimentos",
                "Expanda seu networking estratégico"
              ].map((item, idx) => (
                <div key={idx} className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-brand-accent mt-0.5 mr-3 flex-shrink-0" />
                  <span className="text-gray-800">{item}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="md:w-1/2">
            <div className="relative rounded-xl overflow-hidden shadow-2xl">
              <img 
                src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1170&q=80" 
                alt="Empreendedores trabalhando juntos" 
                className="w-full h-[400px] object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-gray-900/70 to-transparent flex items-end p-8">
                <div className="text-white">
                  <p className="text-xl font-semibold mb-2">Próxima Turma</p>
                  <p className="text-3xl font-bold">15 de Outubro, 2023</p>
                  <p className="mt-2">Vagas Limitadas</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProgramOverview;
