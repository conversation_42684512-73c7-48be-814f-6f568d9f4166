
import React, { useState, useEffect } from "react";
import { Bot, X, SendHorizontal, UserRound, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";

type Message = {
  content: string;
  role: "user" | "assistant";
};

type PurchaseAssistantProps = {
  productName?: string;
  productType?: string;
  className?: string;
};

const PurchaseAssistant = ({ productName, productType, className }: PurchaseAssistantProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  const [inputValue, setInputValue] = useState("");
  const [messages, setMessages] = useState<Message[]>([
    {
      role: "assistant",
      content: productName 
        ? `Olá! Estou aqui para ajudar com informações sobre o programa ${productName}. Tem alguma dúvida específica sobre preço, duração ou conteúdo?`
        : "Olá! Estou aqui para ajudar com informações sobre nossos programas. Tem alguma dúvida específica?"
    }
  ]);

  // Auto-open after 5 seconds, but only once
  useEffect(() => {
    const timer = setTimeout(() => {
      if (!isOpen && !localStorage.getItem('assistantShown')) {
        setIsOpen(true);
        localStorage.setItem('assistantShown', 'true');
      }
    }, 5000);

    return () => clearTimeout(timer);
  }, [isOpen]);

  const handleSendMessage = () => {
    if (inputValue.trim()) {
      // Add user message
      setMessages([...messages, { role: "user", content: inputValue }]);
      
      // Simulate AI response based on keywords
      setTimeout(() => {
        let response = "";
        const lowerCaseInput = inputValue.toLowerCase();
        
        if (lowerCaseInput.includes("preço") || lowerCaseInput.includes("valor") || lowerCaseInput.includes("custo")) {
          response = productName 
            ? `O investimento para o programa ${productName} pode ser parcelado em até 12x no cartão de crédito. Também oferecemos 10% de desconto para pagamento à vista via PIX.`
            : "Nossos programas têm valores a partir de R$ 1.997, dependendo da duração e modalidade. Posso te dar informações mais específicas se me disser qual programa te interessa.";
        } else if (lowerCaseInput.includes("duração") || lowerCaseInput.includes("tempo")) {
          response = productName 
            ? `O programa ${productName} tem duração de ${productType?.includes("Online") ? "4 a 8 semanas" : "3 a 7 dias"}, com acompanhamento completo da nossa equipe.`
            : "Nossos programas variam de 4 semanas a 3 meses para cursos online, e de 3 a 7 dias para imersões presenciais.";
        } else if (lowerCaseInput.includes("inscrever") || lowerCaseInput.includes("matricular") || lowerCaseInput.includes("comprar")) {
          response = "Você pode se inscrever diretamente pelo botão \"Saiba Mais\" abaixo do programa que deseja. Se preferir, posso te colocar em contato com um consultor para esclarecer todas as suas dúvidas antes da compra.";
        } else if (lowerCaseInput.includes("certificado")) {
          response = "Sim, todos os nossos programas emitem certificados de conclusão, que são reconhecidos pelo mercado e podem ser compartilhados no seu LinkedIn.";
        } else if (lowerCaseInput.includes("pagamento") || lowerCaseInput.includes("parcelar")) {
          response = "Aceitamos pagamento via cartão de crédito em até 12x, PIX com 10% de desconto, ou transferência bancária. Qual forma de pagamento seria mais conveniente para você?";
        } else {
          response = "Agradeço sua pergunta! Nosso time de especialistas pode fornecer informações mais detalhadas. Gostaria que eu agendasse uma conversa com um dos nossos consultores?";
        }
        
        setMessages(prev => [...prev, { role: "assistant", content: response }]);
      }, 1000);
      
      setInputValue("");
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      handleSendMessage();
    }
  };

  if (!isOpen) {
    return (
      <Button
        onClick={() => setIsOpen(true)}
        className="rounded-full h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 hover:opacity-90 shadow-lg p-0 fixed bottom-6 right-6 z-40 border-4 border-white animate-pulse"
        aria-label="Assistente de compras"
      >
        <div className="absolute inset-0 rounded-full bg-white/20 animate-ping" style={{ animationDuration: '3s' }}></div>
        <Sparkles className="h-7 w-7 text-white" />
      </Button>
    );
  }

  return (
    <Card 
      className={cn(
        "fixed bottom-6 right-6 z-40 w-80 shadow-xl border border-gray-200 transition-all duration-300 overflow-hidden",
        isMinimized ? "h-14" : "h-96",
        className
      )}
    >
      {/* Header */}
      <div 
        className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-3 flex items-center justify-between cursor-pointer"
        onClick={() => setIsMinimized(!isMinimized)}
      >
        <div className="flex items-center">
          <Sparkles className="h-5 w-5 mr-2" />
          <span className="font-medium">Assistente de Compras</span>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6 text-white hover:bg-white/20 rounded-full p-0"
            onClick={(e) => {
              e.stopPropagation();
              setIsMinimized(!isMinimized);
            }}
          >
            {isMinimized ? "+" : "-"}
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-6 w-6 text-white hover:bg-white/20 rounded-full p-0"
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(false);
            }}
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {!isMinimized && (
        <>
          {/* Messages Container */}
          <div className="flex-1 overflow-auto p-3 space-y-3 h-[calc(100%-110px)]">
            {messages.map((msg, index) => (
              <div
                key={index}
                className={`flex ${
                  msg.role === "assistant" ? "justify-start" : "justify-end"
                }`}
              >
                {msg.role === "assistant" && (
                  <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-center mr-2 flex-shrink-0">
                    <Sparkles className="h-5 w-5 text-white" />
                  </div>
                )}
                <div
                  className={`max-w-[80%] rounded-lg p-2 ${
                    msg.role === "assistant"
                      ? "bg-gray-100 text-gray-800"
                      : "bg-brand text-white"
                  }`}
                >
                  {msg.content}
                </div>
                {msg.role === "user" && (
                  <div className="h-8 w-8 rounded-full bg-brand flex items-center justify-center ml-2 flex-shrink-0">
                    <UserRound className="h-5 w-5 text-white" />
                  </div>
                )}
              </div>
            ))}
          </div>
          
          {/* Input Area */}
          <div className="p-3 border-t border-gray-200 bg-gray-50">
            <div className="flex items-center space-x-2">
              <Input
                placeholder="Digite sua dúvida..."
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button 
                onClick={handleSendMessage} 
                size="icon" 
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white h-9 w-9 p-0"
              >
                <SendHorizontal className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </>
      )}
    </Card>
  );
};

export default PurchaseAssistant;
