
import React from "react";
import { Play } from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";

interface PodcastCardProps {
  imageUrl: string;
  title: string;
  description: string;
  date: string;
  duration: string;
  isNew?: boolean;
  onClick?: () => void;
  className?: string;
}

const PodcastCard = ({
  imageUrl,
  title,
  description,
  date,
  duration,
  isNew = false,
  onClick,
  className,
}: PodcastCardProps) => {
  return (
    <div 
      className={cn(
        "overflow-hidden group rounded-lg border bg-card text-card-foreground shadow-sm transition-all hover:shadow-md",
        className
      )}
    >
      <div className="relative">
        <div className="aspect-square overflow-hidden">
          <img 
            src={imageUrl}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
        </div>
        <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <Button 
            onClick={onClick}
            className="bg-brand-accent hover:bg-brand-accent/90 text-white rounded-full w-14 h-14 flex items-center justify-center"
          >
            <Play size={24} fill="white" className="ml-1" />
          </Button>
        </div>
        {isNew && (
          <div className="absolute top-3 right-3 bg-brand-accent text-white text-xs font-bold py-1 px-2 rounded">
            NOVO
          </div>
        )}
      </div>
      <div className="p-4">
        <h3 className="font-bold text-gray-900 mb-1 line-clamp-2 group-hover:text-brand transition-colors">
          {title}
        </h3>
        <p className="text-gray-600 text-sm mb-3 line-clamp-2">
          {description}
        </p>
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>{date}</span>
          <span>{duration}</span>
        </div>
      </div>
    </div>
  );
};

export default PodcastCard;
