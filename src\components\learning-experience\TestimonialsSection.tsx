
import { useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON>, Quote } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const testimonials = [
  {
    id: 1,
    content: "O programa de aceleração da EmpreenderAcademy foi um divisor de águas para minha startup. A combinação de mentoria qualificada e acesso a investidores nos permitiu triplicar nosso faturamento em apenas 6 meses.",
    author: "<PERSON>",
    role: "CEO, TechSolve",
    image: "https://i.pravatar.cc/150?img=32"
  },
  {
    id: 2,
    content: "Participar dos cursos da EmpreenderAcademy transformou minha visão sobre empreendedorismo. O conteúdo prático e as conexões que fiz me deram confiança para pivotar meu modelo de negócio e encontrar um caminho sustentável.",
    author: "<PERSON>",
    role: "Fundador, EcoDelivery",
    image: "https://i.pravatar.cc/150?img=11"
  },
  {
    id: 3,
    content: "A metodologia hands-on e a rede de contatos que construí durante o programa foram essenciais para conseguirmos nossa primeira rodada de investimento. Recomendo para todo empreendedor que quer acelerar seu crescimento.",
    author: "Mariana Almeida",
    role: "Co-fundadora, FinHealth",
    image: "https://i.pravatar.cc/150?img=5"
  }
];

const videoTestimonials = [
  {
    id: 1,
    videoUrl: "https://www.youtube.com/embed/jfKfPfyJRdk",
    thumbnail: "https://img.youtube.com/vi/jfKfPfyJRdk/hqdefault.jpg",
    author: "Carlos Mendes",
    role: "CEO, InnovaTech",
    title: "Como escalei minha startup em 6 meses"
  },
  {
    id: 2,
    videoUrl: "https://www.youtube.com/embed/ceqgwo7U28Y",
    thumbnail: "https://img.youtube.com/vi/ceqgwo7U28Y/hqdefault.jpg",
    author: "Amanda Silva",
    role: "Fundadora, EcoSolutions",
    title: "Minha jornada com o programa de aceleração"
  },
  {
    id: 3,
    videoUrl: "https://www.youtube.com/embed/n61ULEU7CO0",
    thumbnail: "https://img.youtube.com/vi/n61ULEU7CO0/hqdefault.jpg",
    author: "Ricardo Oliveira",
    role: "CTO, DataFlex",
    title: "Como reformulamos nosso modelo de negócio"
  }
];

const TestimonialsSection = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [activeVideoIndex, setActiveVideoIndex] = useState(0);
  const [showVideo, setShowVideo] = useState(false);

  const nextTestimonial = () => {
    setActiveIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setActiveIndex((prevIndex) => 
      prevIndex === 0 ? testimonials.length - 1 : prevIndex - 1
    );
  };

  const nextVideoTestimonial = () => {
    setActiveVideoIndex((prevIndex) => (prevIndex + 1) % videoTestimonials.length);
  };

  const prevVideoTestimonial = () => {
    setActiveVideoIndex((prevIndex) => 
      prevIndex === 0 ? videoTestimonials.length - 1 : prevIndex - 1
    );
  };

  const toggleTestimonialType = () => {
    setShowVideo(!showVideo);
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            O que <span className="text-brand">nossos alunos</span> dizem
          </h2>
          <p className="text-lg text-gray-700 max-w-3xl mx-auto mb-8">
            Histórias reais de empreendedores que transformaram seus negócios com nossos programas.
          </p>
          
          <div className="flex justify-center mb-12">
            <div className="inline-flex rounded-md shadow-sm" role="group">
              <button
                type="button"
                onClick={() => setShowVideo(false)}
                className={`px-4 py-2 text-sm font-medium rounded-l-lg border ${
                  !showVideo 
                    ? "bg-brand text-white border-brand" 
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-100"
                }`}
              >
                Depoimentos em Texto
              </button>
              <button
                type="button"
                onClick={() => setShowVideo(true)}
                className={`px-4 py-2 text-sm font-medium rounded-r-lg border ${
                  showVideo 
                    ? "bg-brand text-white border-brand" 
                    : "bg-white text-gray-700 border-gray-200 hover:bg-gray-100"
                }`}
              >
                Depoimentos em Vídeo
              </button>
            </div>
          </div>
        </div>
        
        {!showVideo ? (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl p-8 md:p-10 shadow-xl relative">
              <Quote className="absolute top-6 left-6 h-12 w-12 text-brand-light opacity-50" />
              
              <div className="relative z-10">
                <p className="text-xl text-gray-800 mb-8 pl-10">
                  {testimonials[activeIndex].content}
                </p>
                
                <div className="flex items-center">
                  <img 
                    src={testimonials[activeIndex].image} 
                    alt={testimonials[activeIndex].author}
                    className="w-16 h-16 rounded-full object-cover mr-4"
                  />
                  <div>
                    <h4 className="font-medium text-gray-900">{testimonials[activeIndex].author}</h4>
                    <p className="text-gray-600">{testimonials[activeIndex].role}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-center mt-8 space-x-4">
              <Button 
                variant="outline" 
                size="icon" 
                className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
                onClick={prevTestimonial}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    index === activeIndex ? "bg-brand" : "bg-gray-300"
                  }`}
                  onClick={() => setActiveIndex(index)}
                />
              ))}
              
              <Button 
                variant="outline" 
                size="icon"
                className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
                onClick={nextTestimonial}
              >
                <ArrowRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        ) : (
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-xl overflow-hidden shadow-xl">
              <div className="aspect-w-16 aspect-h-9 relative">
                <iframe
                  src={videoTestimonials[activeVideoIndex].videoUrl}
                  title={`Depoimento de ${videoTestimonials[activeVideoIndex].author}`}
                  className="w-full h-[400px]"
                  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                  allowFullScreen
                ></iframe>
              </div>
              
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-2">{videoTestimonials[activeVideoIndex].title}</h3>
                <div className="flex items-center">
                  <div>
                    <h4 className="font-medium text-gray-900">{videoTestimonials[activeVideoIndex].author}</h4>
                    <p className="text-gray-600">{videoTestimonials[activeVideoIndex].role}</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="flex justify-center mt-8 space-x-4">
              <Button 
                variant="outline" 
                size="icon" 
                className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
                onClick={prevVideoTestimonial}
              >
                <ArrowLeft className="h-5 w-5" />
              </Button>
              
              {videoTestimonials.map((_, index) => (
                <button
                  key={index}
                  className={`w-3 h-3 rounded-full ${
                    index === activeVideoIndex ? "bg-brand" : "bg-gray-300"
                  }`}
                  onClick={() => setActiveVideoIndex(index)}
                />
              ))}
              
              <Button 
                variant="outline" 
                size="icon"
                className="bg-white text-brand-accent border-brand-accent hover:bg-brand-accent hover:text-white"
                onClick={nextVideoTestimonial}
              >
                <ArrowRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        )}
      </div>
    </section>
  );
};

export default TestimonialsSection;
