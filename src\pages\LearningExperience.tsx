
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import AIAssistant from "@/components/AIAssistant";
import PurchaseAssistant from "@/components/PurchaseAssistant";
import CallToAction from "@/components/learning-experience/CallToAction";
import BenefitsSection from "@/components/learning-experience/BenefitsSection";
import TestimonialsSection from "@/components/learning-experience/TestimonialsSection";
import UpcomingExperiences from "@/components/learning-experience/UpcomingExperiences";
import InnovationEcosystems from "@/components/learning-experience/InnovationEcosystems";

const LearningExperience = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Learning Experience | ATAC";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <section className="pt-20 pb-16 md:pt-32 md:pb-24 bg-gradient-to-br from-blue-900 via-indigo-900 to-violet-900 text-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <h1 className="font-montserrat text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Experiências de Aprendizado Globais
            </h1>
            <p className="text-xl opacity-90 mb-8 leading-relaxed">
              Descubra os ecossistemas de inovação mais avançados do mundo em viagens imersivas e transformadoras com a ATAC.
            </p>
          </div>
        </div>
      </section>

      <InnovationEcosystems />
      <BenefitsSection />
      <UpcomingExperiences />
      <TestimonialsSection />
      <CallToAction />
      
      {/* New Purchase Assistant */}
      <PurchaseAssistant 
        productName="Learning Experience" 
        productType="Presencial Internacional"
      />

      <Footer />
    </div>
  );
};

export default LearningExperience;
