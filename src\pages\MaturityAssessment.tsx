import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, ArrowRight, CheckCircle, Info, BarChart3, Target, Users, Lightbulb, Clock, Award, Download, MessageSquare } from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import AIAssistant from "@/components/AIAssistant";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

// Import assessment questions
import { assessmentQuestions } from "@/data/assessmentQuestions";

// Generate form schema dynamically based on all questions
const generateFormSchema = () => {
  const schemaMap: Record<string, z.ZodString> = {};
  
  assessmentQuestions.forEach((question) => {
    const fieldName = `${question.category}-${question.id}`;
    schemaMap[fieldName] = z.string();
  });
  
  return z.object(schemaMap);
};

const formSchema = generateFormSchema();

// Category information for better UX
const categoryInfo = {
  solution: {
    title: "Solução",
    description: "Avaliação da escalabilidade, inovação e validação da sua solução",
    icon: Lightbulb,
    color: "text-blue-600"
  },
  mindset: {
    title: "Mindset",
    description: "Conhecimento e mentalidade empreendedora da equipe",
    icon: Users,
    color: "text-green-600"
  },
  team: {
    title: "Equipe",
    description: "Estrutura e competências do time",
    icon: Users,
    color: "text-purple-600"
  },
  product: {
    title: "Produto/Serviço",
    description: "Grau de validação e desenvolvimento da solução",
    icon: Target,
    color: "text-orange-600"
  },
  project: {
    title: "Gestão de Projetos",
    description: "Metodologias ágeis e gestão de processos",
    icon: BarChart3,
    color: "text-red-600"
  },
  pitch: {
    title: "Pitch",
    description: "Capacidade de apresentação e comunicação",
    icon: MessageSquare,
    color: "text-indigo-600"
  }
};

const MaturityAssessment = () => {
  const [currentCategory, setCurrentCategory] = useState<string>("solution");
  const [progress, setProgress] = useState<number>(0);
  const [completedCategories, setCompletedCategories] = useState<Set<string>>(new Set());
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {}
  });
  
  // Calculate progress whenever form values change
  useEffect(() => {
    const values = form.getValues();
    const totalQuestions = Object.keys(formSchema.shape).length;
    const answeredQuestions = Object.values(values).filter(Boolean).length;
    setProgress(Math.round((answeredQuestions / totalQuestions) * 100));
  }, [form.watch()]);
  
  // Set page title and scroll to top on load
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Assessment de Maturidade | ATAC Academy";
  }, []);
  
  // Define all categories with their display names
  const categories = {
    "solution": "SOLUÇÃO",
    "mindset": "MINDSET",
    "team": "EQUIPE",
    "product": "PRODUTO / SERVIÇO",
    "business": "NEGÓCIOS",
    "project": "GESTÃO DE PROJETOS",
    "marketing": "MARKETING E VENDAS",
    "legal": "JURÍDICO E CONTÁBIL",
    "pitch": "PITCH",
    "networking": "NETWORKING E ECOSSISTEMA"
  };
  
  // Navigate to different category
  const navigateToCategory = (category: string) => {
    setCurrentCategory(category);
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };
  
  // Calculate next and previous categories
  const getCategoryIndex = () => {
    return Object.keys(categories).indexOf(currentCategory);
  };
  
  const getNextCategory = () => {
    const keys = Object.keys(categories);
    const currentIndex = getCategoryIndex();
    return currentIndex < keys.length - 1 ? keys[currentIndex + 1] : null;
  };
  
  const getPreviousCategory = () => {
    const keys = Object.keys(categories);
    const currentIndex = getCategoryIndex();
    return currentIndex > 0 ? keys[currentIndex - 1] : null;
  };
  
  // Handle form submission
  const onSubmit = (data: z.infer<typeof formSchema>) => {
    console.log("Assessment data:", data);
    
    toast({
      title: "Diagnóstico Concluído!",
      description: "Enviamos seu resultado por email. Em breve nossa equipe entrará em contato.",
    });
    
    setTimeout(() => {
      navigate("/diagnostic");
    }, 2000);
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="mb-10">
            {/* Header */}
            <div className="text-center mb-8">
              <h1 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                Assessment de <span className="text-brand-accent">Maturidade</span>
              </h1>
              <p className="text-lg text-gray-700 mb-6">
                Avalie o estágio atual da sua startup e receba recomendações personalizadas para crescer.
              </p>

              {/* Benefits */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 max-w-4xl mx-auto mb-8">
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4 text-center">
                    <BarChart3 className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-blue-900">Análise Completa</h3>
                    <p className="text-sm text-blue-700">6 dimensões avaliadas</p>
                  </CardContent>
                </Card>
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-4 text-center">
                    <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-green-900">Roadmap Personalizado</h3>
                    <p className="text-sm text-green-700">Próximos passos claros</p>
                  </CardContent>
                </Card>
                <Card className="bg-purple-50 border-purple-200">
                  <CardContent className="p-4 text-center">
                    <Award className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-purple-900">Benchmarking</h3>
                    <p className="text-sm text-purple-700">Compare com o mercado</p>
                  </CardContent>
                </Card>
              </div>
            </div>

            {/* Progress Section */}
            <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Progresso do Assessment</h2>
                <div className="flex items-center text-sm text-gray-600">
                  <Clock className="h-4 w-4 mr-1" />
                  Tempo estimado: 30-45 min
                </div>
              </div>

              <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
                <div
                  className="bg-gradient-to-r from-brand-accent to-brand h-3 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">{progress}% completo</span>
                <span className="text-gray-500">{Object.keys(categoryInfo).length} seções</span>
              </div>
            </div>
          </div>
          
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              {/* Category navigation */}
              <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                <h3 className="text-lg font-semibold mb-4 text-center">Seções do Assessment</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
                  {Object.entries(categories).map(([key, label]) => {
                    const info = categoryInfo[key];
                    const Icon = info?.icon || Target;
                    const isCompleted = completedCategories.has(key);
                    const isCurrent = currentCategory === key;

                    return (
                      <button
                        key={key}
                        type="button"
                        onClick={() => navigateToCategory(key)}
                        className={`p-3 rounded-lg text-left transition-all duration-200 border-2 ${
                          isCurrent
                            ? 'border-brand-accent bg-brand-accent/5 shadow-md'
                            : isCompleted
                            ? 'border-green-200 bg-green-50 hover:border-green-300'
                            : 'border-gray-200 bg-gray-50 hover:border-gray-300'
                        }`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <Icon className={`h-4 w-4 ${
                            isCurrent ? 'text-brand-accent' :
                            isCompleted ? 'text-green-600' : 'text-gray-500'
                          }`} />
                          {isCompleted && (
                            <CheckCircle className="h-3 w-3 text-green-600" />
                          )}
                        </div>
                        <h4 className={`font-medium text-xs ${
                          isCurrent ? 'text-brand-accent' :
                          isCompleted ? 'text-green-900' : 'text-gray-900'
                        }`}>
                          {info?.title || label}
                        </h4>
                      </button>
                    );
                  })}
                </div>
              </div>
              
              {/* Current category info */}
              <div className="bg-gradient-to-r from-brand-accent/10 to-brand/10 p-6 rounded-xl mb-8 border border-brand-accent/20">
                <div className="flex items-center mb-3">
                  {categoryInfo[currentCategory]?.icon && (() => {
                    const IconComponent = categoryInfo[currentCategory].icon;
                    return <IconComponent className="h-6 w-6 text-brand-accent mr-3" />;
                  })()}
                  <h2 className="font-montserrat text-xl font-semibold text-gray-900">
                    {categories[currentCategory as keyof typeof categories]}
                  </h2>
                </div>
                {categoryInfo[currentCategory]?.description && (
                  <p className="text-gray-700 text-sm">
                    {categoryInfo[currentCategory].description}
                  </p>
                )}
                <div className="mt-3 flex items-center text-sm text-gray-600">
                  <span className="mr-4">
                    Perguntas: {assessmentQuestions.filter(q => q.category === currentCategory).length}
                  </span>
                  <span>
                    Seção {Object.keys(categories).indexOf(currentCategory) + 1} de {Object.keys(categories).length}
                  </span>
                </div>
              </div>
              
              {/* Render questions for current category */}
              <div className="space-y-10">
                {assessmentQuestions
                  .filter(q => q.category === currentCategory)
                  .map((question, index) => (
                    <FormField
                      key={question.id}
                      control={form.control}
                      name={`${question.category}-${question.id}` as any}
                      render={({ field }) => (
                        <FormItem className="space-y-3">
                          <FormLabel className="text-base font-semibold flex items-start gap-2">
                            {index + 1} - {question.question}
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Info className="h-4 w-4 text-gray-500" />
                                </TooltipTrigger>
                                <TooltipContent className="max-w-xs">
                                  <p>Escolha a opção que melhor representa o estágio atual da sua startup.</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </FormLabel>
                          <FormControl>
                            <Select 
                              onValueChange={field.onChange} 
                              defaultValue={field.value}
                            >
                              <SelectTrigger className="w-full">
                                <SelectValue placeholder="Selecione uma opção" />
                              </SelectTrigger>
                              <SelectContent>
                                {question.options.map((option, i) => (
                                  <SelectItem key={i} value={String(i)}>
                                    {option}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  ))
                }
              </div>
              
              {/* Navigation buttons */}
              <div className="flex justify-between pt-6">
                {getPreviousCategory() ? (
                  <Button 
                    type="button"
                    variant="outline" 
                    onClick={() => navigateToCategory(getPreviousCategory() as string)}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    {categories[getPreviousCategory() as keyof typeof categories]}
                  </Button>
                ) : (
                  <div></div>
                )}
                
                {getNextCategory() ? (
                  <Button 
                    type="button"
                    onClick={() => navigateToCategory(getNextCategory() as string)}
                    className="bg-brand-accent hover:bg-brand-accent/90"
                  >
                    {categories[getNextCategory() as keyof typeof categories]}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                ) : (
                  <Button 
                    type="submit"
                    className="bg-green-600 hover:bg-green-700"
                  >
                    Finalizar Assessment
                    <CheckCircle className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </main>
      <Footer />
      
      {/* Add the AI Assistant */}
      <AIAssistant context="assessment" />
    </div>
  );
};

export default MaturityAssessment;
