
import { Link } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import PurchaseAssistant from "@/components/PurchaseAssistant";

interface FaqTabProps {
  course: any;
  onEnrollmentClick?: () => void;
}

const FaqTab = ({ course, onEnrollmentClick }: FaqTabProps) => {
  return (
    <div className="container mx-auto px-4">
      <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-10">
        Perguntas Frequentes
      </h2>
      
      <div className="space-y-6 max-w-3xl mx-auto">
        {course.faq.map((item, index) => (
          <div key={index} className="border border-gray-200 rounded-lg overflow-hidden">
            <div className="p-6">
              <h4 className="font-montserrat text-xl font-semibold text-gray-900 mb-4">
                {item.question}
              </h4>
              <p className="text-lg text-gray-700">
                {item.answer}
              </p>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-12 text-center space-y-6">
        {onEnrollmentClick ? (
          <div className="bg-gray-50 rounded-lg p-8 max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold mb-4">Pronto para dar o próximo passo?</h3>
            <p className="text-lg text-gray-700 mb-6">
              Garanta sua vaga agora mesmo no curso {course.title} e comece sua jornada de aprendizado!
            </p>
            <Button 
              onClick={onEnrollmentClick} 
              className="bg-brand-accent hover:bg-brand-accent/90 text-white py-6 px-8 text-lg"
            >
              Inscreva-se Agora
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <p className="mt-4 text-sm text-gray-500">
              Garantia de 7 dias ou seu dinheiro de volta
            </p>
          </div>
        ) : (
          <div>
            <p className="text-lg text-gray-700 mb-6">
              Ainda tem dúvidas? Entre em contato com nossa equipe
            </p>
            <Link to="/contato">
              <Button variant="outline" className="border-brand text-brand hover:bg-brand hover:text-white">
                Fale Conosco
              </Button>
            </Link>
          </div>
        )}
      </div>
      
      {/* Purchase Assistant for the FAQ page */}
      <div className="hidden lg:block">
        <PurchaseAssistant 
          productName={course.title}
          productType={course.type}
        />
      </div>
    </div>
  );
};

export default FaqTab;
