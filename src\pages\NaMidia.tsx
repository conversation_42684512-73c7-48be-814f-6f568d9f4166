import React, { useState, useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { ChevronRight, Calendar, ArrowUpRight, Search, Clock, Newspaper, Award, Video, Bookmark } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";

interface MediaItem {
  id: number;
  title: string;
  source: string;
  date: string;
  image: string;
  link: string;
  category: "imprensa" | "artigos" | "eventos" | "premios";
  description?: string;
  featured?: boolean;
}

const NaMidia = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedYear, setSelectedYear] = useState<string>("todos");
  const [currentTab, setCurrentTab] = useState<string>("imprensa");
  const [filteredMedia, setFilteredMedia] = useState<MediaItem[]>([]);
  
  const mediaItems: MediaItem[] = [
    {
      id: 1,
      title: "ATAC lança novo programa de aceleração para startups",
      source: "Exame",
      date: "2023-10-15",
      image: "/lovable-uploads/24cdc228-06f3-443c-b02e-e30796c20334.png",
      link: "https://exame.com",
      category: "imprensa",
      description: "A ATAC anuncia seu mais novo programa de aceleração focado em startups de tecnologia e sustentabilidade, com mentoria de especialistas do mercado.",
      featured: true
    },
    {
      id: 2,
      title: "Como a inovação aberta está transformando o mercado brasileiro",
      source: "Valor Econômico",
      date: "2023-08-22",
      image: "/lovable-uploads/b5c971b0-e98b-4117-b372-0b46ff1ce95b.png",
      link: "https://valor.com.br",
      category: "artigos",
      description: "Artigo escrito por nosso CEO discute os modelos de inovação aberta e seu impacto nas corporações brasileiras."
    },
    {
      id: 3,
      title: "ATAC participa do maior evento de inovação da América Latina",
      source: "StartupOn",
      date: "2023-06-05",
      image: "/lovable-uploads/ebdd26ce-4835-4e4c-91b6-abbfad51eac7.png",
      link: "https://startupon.com.br",
      category: "eventos",
      description: "Nossa equipe marcou presença no maior evento de inovação da América Latina, apresentando cases de sucesso e novas metodologias."
    },
    {
      id: 4,
      title: "Programa de inovação da ATAC é premiado internacionalmente",
      source: "TechCrunch",
      date: "2023-04-18",
      image: "/lovable-uploads/8a5b8e81-4db4-47e3-a85f-96bd17f725f5.png",
      link: "https://techcrunch.com",
      category: "premios",
      description: "O programa de inovação corporativa da ATAC recebeu reconhecimento internacional por seus resultados expressivos.",
      featured: true
    },
    {
      id: 5,
      title: "CEO da ATAC fala sobre tendências de inovação para 2024",
      source: "InfoMoney",
      date: "2024-01-10",
      image: "/lovable-uploads/d2cd3af4-591e-4991-b1b0-054a49a508d1.png",
      link: "https://infomoney.com.br",
      category: "imprensa",
      description: "Em entrevista exclusiva, nosso CEO compartilha insights sobre as principais tendências de inovação para 2024."
    },
    {
      id: 6,
      title: "Por que investir em inovação aberta é essencial para grandes empresas",
      source: "Harvard Business Review Brasil",
      date: "2024-02-14",
      image: "/lovable-uploads/d7ee6582-3626-4fef-ab16-e533794ce84f.png",
      link: "https://hbrbr.com.br",
      category: "artigos",
      description: "Artigo assinado por nosso diretor de inovação explora os benefícios da inovação aberta para corporações tradicionais."
    }
  ];

  const featuredMedia = mediaItems.filter(item => item.featured);

  useEffect(() => {
    let filtered = [...mediaItems];
    
    if (currentTab !== "todos") {
      filtered = filtered.filter(item => item.category === currentTab);
    }
    
    if (selectedYear !== "todos") {
      filtered = filtered.filter(item => {
        const itemYear = new Date(item.date).getFullYear().toString();
        return itemYear === selectedYear;
      });
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(
        item => 
          item.title.toLowerCase().includes(query) || 
          item.source.toLowerCase().includes(query) ||
          (item.description && item.description.toLowerCase().includes(query))
      );
    }
    
    setFilteredMedia(filtered);
  }, [searchQuery, selectedYear, currentTab]);

  const years = ["todos", ...new Set(mediaItems.map(item => 
    new Date(item.date).getFullYear().toString()
  ))].sort((a, b) => b.localeCompare(a));

  const categoryIcons = {
    imprensa: <Newspaper className="h-4 w-4" />,
    artigos: <Clock className="h-4 w-4" />,
    eventos: <Video className="h-4 w-4" />,
    premios: <Award className="h-4 w-4" />
  };

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "ATAC Mídia e Eventos | Aparições e Publicações";
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('pt-BR', { 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    }).format(date);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Navbar />
      
      <main className="flex-1 bg-gray-50">
        <div className="bg-brand-dark text-white py-24">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-6 font-montserrat">
                Mídia e Eventos
              </h1>
              <p className="text-xl opacity-90 mb-8">
                Acompanhe nossas aparições na imprensa, artigos, eventos e reconhecimentos
              </p>
              <div className="flex items-center">
                <div className="w-20 h-1 bg-brand-accent rounded-full"></div>
              </div>
            </div>
          </div>
        </div>
        
        {featuredMedia.length > 0 && (
          <div className="py-16 bg-white border-b">
            <div className="container mx-auto px-4">
              <div className="flex items-center justify-between mb-10">
                <h2 className="text-2xl font-bold">Destaques na Mídia</h2>
                <Badge variant="outline" className="border-brand-accent text-brand-accent">
                  <Bookmark className="mr-1 h-3 w-3" /> Em destaque
                </Badge>
              </div>
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-10">
                {featuredMedia.map((item) => (
                  <Card key={item.id} className="overflow-hidden transition-all duration-300 hover:shadow-lg border-0 shadow-md flex flex-col h-full">
                    <div className="aspect-video relative overflow-hidden bg-gray-100">
                      <img 
                        src={item.image} 
                        alt={item.title} 
                        className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                      />
                      <div className="absolute bottom-0 left-0 w-full p-4 bg-gradient-to-t from-black/80 to-transparent">
                        <span className="text-white font-medium text-sm bg-brand-accent px-2 py-1 rounded">
                          {item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                        </span>
                      </div>
                    </div>
                    <CardContent className="p-8 flex-grow">
                      <div className="flex items-center text-sm text-gray-500 mb-4 gap-2">
                        <span className="font-medium text-brand-accent">{item.source}</span>
                        <span>•</span>
                        <div className="flex items-center">
                          <Calendar size={14} className="mr-1" />
                          {formatDate(item.date)}
                        </div>
                      </div>
                      <h3 className="font-bold text-xl mb-4 hover:text-brand-accent transition-colors">
                        <a href={item.link} target="_blank" rel="noopener noreferrer">
                          {item.title}
                        </a>
                      </h3>
                      <p className="text-gray-600 mb-5 line-clamp-3">{item.description}</p>
                    </CardContent>
                    <CardFooter className="pt-0 pb-8 px-8">
                      <a 
                        href={item.link} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-flex items-center text-brand-accent hover:underline font-medium"
                      >
                        Leia a matéria completa
                        <ArrowUpRight size={16} className="ml-1" />
                      </a>
                    </CardFooter>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        )}
        
        <div className="bg-gray-100 py-10 sticky top-0 z-10 shadow-sm mt-10">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row gap-6 justify-between">
              <div className="w-full md:w-1/3 relative">
                <Input
                  type="text"
                  placeholder="Buscar na mídia..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-white"
                />
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
              </div>
              
              <div className="flex flex-wrap gap-3">
                {years.map((year) => (
                  <Button
                    key={year}
                    variant={selectedYear === year ? "default" : "outline"}
                    onClick={() => setSelectedYear(year)}
                    className="min-w-20"
                  >
                    {year === "todos" ? "Todos" : year}
                  </Button>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        <div className="py-16 bg-white">
          <div className="container mx-auto px-4">
            <Tabs defaultValue="imprensa" onValueChange={setCurrentTab} className="w-full">
              <div className="border-b mb-10">
                <TabsList className="bg-transparent space-x-10">
                  <TabsTrigger 
                    value="imprensa"
                    className="text-gray-700 font-medium text-base data-[state=active]:text-brand-accent data-[state=active]:border-b-2 data-[state=active]:border-brand-accent pb-2 rounded-none"
                  >
                    <Newspaper className="mr-2 h-4 w-4" />
                    Imprensa
                  </TabsTrigger>
                  <TabsTrigger 
                    value="artigos"
                    className="text-gray-700 font-medium text-base data-[state=active]:text-brand-accent data-[state=active]:border-b-2 data-[state=active]:border-brand-accent pb-2 rounded-none"
                  >
                    <Clock className="mr-2 h-4 w-4" />
                    Artigos
                  </TabsTrigger>
                  <TabsTrigger 
                    value="eventos"
                    className="text-gray-700 font-medium text-base data-[state=active]:text-brand-accent data-[state=active]:border-b-2 data-[state=active]:border-brand-accent pb-2 rounded-none"
                  >
                    <Video className="mr-2 h-4 w-4" />
                    Eventos
                  </TabsTrigger>
                  <TabsTrigger 
                    value="premios"
                    className="text-gray-700 font-medium text-base data-[state=active]:text-brand-accent data-[state=active]:border-b-2 data-[state=active]:border-brand-accent pb-2 rounded-none"
                  >
                    <Award className="mr-2 h-4 w-4" />
                    Prêmios
                  </TabsTrigger>
                </TabsList>
              </div>
              
              {["imprensa", "artigos", "eventos", "premios"].map((tab) => (
                <TabsContent key={tab} value={tab} className="mt-0">
                  {filteredMedia.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                      {filteredMedia.map((item) => (
                        <Card key={item.id} className="overflow-hidden transition-all duration-300 hover:shadow-lg border border-gray-100 shadow-sm h-full flex flex-col">
                          <div className="aspect-video relative overflow-hidden bg-gray-100">
                            <img 
                              src={item.image} 
                              alt={item.title} 
                              className="w-full h-full object-cover transition-transform duration-300 hover:scale-105"
                            />
                            <div className="absolute top-0 right-0 m-3">
                              {categoryIcons[item.category]}
                            </div>
                          </div>
                          <CardContent className="p-8 flex-grow">
                            <div className="flex items-center text-sm text-gray-500 mb-4 gap-2">
                              <span className="font-medium text-brand-accent">{item.source}</span>
                              <span>•</span>
                              <div className="flex items-center">
                                <Calendar size={14} className="mr-1" />
                                {formatDate(item.date)}
                              </div>
                            </div>
                            <h3 className="font-semibold text-lg mb-4 line-clamp-2 hover:text-brand-accent transition-colors">
                              <a href={item.link} target="_blank" rel="noopener noreferrer">
                                {item.title}
                              </a>
                            </h3>
                            {item.description && (
                              <p className="text-gray-600 line-clamp-3 mb-5">{item.description}</p>
                            )}
                          </CardContent>
                          <CardFooter className="pt-0 pb-8 px-8">
                            <a 
                              href={item.link} 
                              target="_blank" 
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-brand-accent hover:underline font-medium"
                            >
                              Ler mais
                              <ArrowUpRight size={16} className="ml-1" />
                            </a>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-16">
                      <p className="text-lg text-gray-500">Nenhum resultado encontrado para sua busca.</p>
                    </div>
                  )}
                </TabsContent>
              ))}
            </Tabs>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
};

export default NaMidia;
