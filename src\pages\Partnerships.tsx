
import { useEffect } from "react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { ArrowRight, Users, Building, Trophy, HandshakeIcon } from "lucide-react";

const partnersData = [
  {
    name: "Venture Capital XYZ",
    logo: "https://images.unsplash.com/photo-1560179707-f14e90ef3623?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80",
    description: "Fundo de investimento especializado em startups de tecnologia em estágio inicial.",
    type: "Investidor"
  },
  {
    name: "Aceleradora Beta",
    logo: "https://images.unsplash.com/photo-1559136555-9303baea8ebd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    description: "Programa de aceleração para startups com foco em impacto social e sustentabilidade.",
    type: "Aceleradora"
  },
  {
    name: "TechHub Coworking",
    logo: "https://images.unsplash.com/photo-1497366754035-f200968a6e72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80",
    description: "Espaço colaborativo para startups e empreendedores com infraestrutura completa.",
    type: "Espaço"
  },
  {
    name: "Universidade Inovação",
    logo: "https://images.unsplash.com/photo-1523050854058-8df90110c9f1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    description: "Centro de pesquisa e desenvolvimento com laboratórios e recursos para startups.",
    type: "Educação"
  },
  {
    name: "Consultoria Estratégica",
    logo: "https://images.unsplash.com/photo-1507537297725-24a1c029d3ca?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1074&q=80",
    description: "Consultoria especializada em estratégia de negócios e mentoria para empreendedores.",
    type: "Consultoria"
  },
  {
    name: "Hub de Inovação",
    logo: "https://images.unsplash.com/photo-1542744094-3a31f272c490?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80",
    description: "Centro de inovação corporativa com programas de conexão com startups.",
    type: "Hub"
  }
];

const benefitsData = [
  {
    title: "Networking Estratégico",
    description: "Acesso a uma rede exclusiva de contatos e oportunidades de negócio",
    icon: Users
  },
  {
    title: "Recursos Compartilhados",
    description: "Infraestrutura, tecnologia e ferramentas para impulsionar seu negócio",
    icon: Building
  },
  {
    title: "Reconhecimento de Mercado",
    description: "Credibilidade e visibilidade junto ao ecossistema de inovação",
    icon: Trophy
  },
  {
    title: "Oportunidades de Negócio",
    description: "Conexão com potenciais clientes, investidores e parceiros estratégicos",
    icon: HandshakeIcon
  }
];

const Partnerships = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Parcerias Estratégicas | EmpreenderAcademy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h1 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Parcerias <span className="text-brand">Estratégicas</span>
            </h1>
            <p className="text-xl text-gray-700">
              Conectamos seu negócio ao ecossistema de inovação através de parcerias com organizações líderes de mercado.
            </p>
          </div>
          
          <div className="bg-gray-50 rounded-xl p-8 mb-20">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-6">
                  Por que se tornar um parceiro?
                </h2>
                <p className="text-lg text-gray-700 mb-6">
                  Nossa rede de parcerias estratégicas cria valor para todos os envolvidos, conectando startups, investidores, mentores e organizações de apoio.
                </p>
                
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-8">
                  {benefitsData.map((benefit, index) => (
                    <div key={index} className="flex items-start">
                      <div className="w-10 h-10 rounded-full bg-brand-light flex items-center justify-center mr-4">
                        <benefit.icon className="h-5 w-5 text-brand" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 mb-1">{benefit.title}</h3>
                        <p className="text-gray-700 text-sm">{benefit.description}</p>
                      </div>
                    </div>
                  ))}
                </div>
                
                <Link to="/contato">
                  <Button className="bg-brand hover:bg-brand-secondary">
                    Torne-se um Parceiro
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
              </div>
              
              <div>
                <img 
                  src="https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80" 
                  alt="Parcerias Estratégicas" 
                  className="rounded-xl shadow-lg w-full h-auto"
                />
              </div>
            </div>
          </div>
          
          <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-10 text-center">
            Nossos Parceiros
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {partnersData.map((partner, index) => (
              <div key={index} className="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <div className="h-48 overflow-hidden">
                  <img 
                    src={partner.logo} 
                    alt={partner.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-6">
                  <div className="inline-block px-3 py-1 text-xs font-medium bg-brand-light text-brand rounded-full mb-3">
                    {partner.type}
                  </div>
                  <h3 className="font-semibold text-xl mb-2">{partner.name}</h3>
                  <p className="text-gray-700 mb-4">{partner.description}</p>
                  <Button variant="link" className="text-brand hover:text-brand-secondary p-0">
                    Conhecer Mais
                    <ArrowRight className="ml-1 h-4 w-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-brand text-white rounded-xl p-8 text-center">
            <h3 className="font-montserrat text-2xl font-bold mb-4">
              Interessado em fazer parte da nossa rede de parceiros?
            </h3>
            <p className="text-white/90 max-w-2xl mx-auto mb-6">
              Entre em contato conosco para discutir oportunidades de parceria e como podemos criar valor juntos.
            </p>
            <Link to="/contato">
              <Button className="bg-white text-brand hover:bg-gray-100">
                Entre em Contato
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
};

export default Partnerships;
