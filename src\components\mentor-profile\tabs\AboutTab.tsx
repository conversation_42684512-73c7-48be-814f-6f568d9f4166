
import { CheckCircle } from "lucide-react";

interface AboutTabProps {
  mentor: any;
  benefits: string[];
}

const AboutTab = ({ mentor, benefits }: AboutTabProps) => {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-bold mb-3">Sobre {mentor.name.split(' ')[0]}</h2>
        <p className="text-gray-700 mb-6 text-lg leading-relaxed">
          {mentor.bio} 
          {!mentor.bio.endsWith('.') && '.'}
          {' '}Com mais de {5 + mentor.sessions % 10} anos de experiência no mercado, 
          {mentor.name.split(' ')[0]} tem ajudado empreendedores a superar desafios 
          e escalar seus negócios com estratégias eficientes e personalizadas.
        </p>
        <p className="text-gray-700 mb-6 text-lg leading-relaxed">
          Focado em resultados tangíveis, ele desenvolve soluções adaptadas às necessidades 
          específicas de cada empreendedor, compartilhando dicas práticas e insights valiosos.
        </p>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-3">Como posso te ajudar</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {mentor.expertise.map((skill: string, idx: number) => (
            <div key={idx} className="flex items-start gap-2">
              <CheckCircle className="h-5 w-5 text-brand flex-shrink-0 mt-1" />
              <div>
                <h3 className="font-bold text-gray-800">{skill}</h3>
                <p className="text-gray-600 text-sm">
                  Estratégias e práticas para implementar {skill.toLowerCase()} no seu negócio.
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div>
        <h2 className="text-xl font-bold mb-3">Benefícios da mentoria</h2>
        <div className="space-y-3 mb-6">
          {benefits.map((benefit, idx) => (
            <div key={idx} className="flex items-start gap-2">
              <CheckCircle className="h-5 w-5 text-brand flex-shrink-0 mt-0.5" />
              <p className="text-gray-700">{benefit}</p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AboutTab;
