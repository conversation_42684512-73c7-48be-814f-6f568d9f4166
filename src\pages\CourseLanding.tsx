
import { useEffect, useState } from "react";
import { useParams, useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { TabsContent } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";

// Import components for course landing page
import CourseHero from "@/components/course-landing/CourseHero";
import CourseNavTabs from "@/components/course-landing/CourseNavTabs";
import PaymentDialog from "@/components/course-landing/PaymentDialog";
import OverviewTab from "@/components/course-landing/tabs/OverviewTab";
import CurriculumTab from "@/components/course-landing/tabs/CurriculumTab";
import InstructorTab from "@/components/course-landing/tabs/InstructorTab";
import FaqTab from "@/components/course-landing/tabs/FaqTab";

// Import course data
import { courses } from "@/components/course-landing/coursesData";

const CourseLanding = () => {
  const { courseId } = useParams();
  const [course, setCourse] = useState(null);
  const [activeTab, setActiveTab] = useState("overview");
  const [showPaymentDialog, setShowPaymentDialog] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();
  
  useEffect(() => {
    window.scrollTo(0, 0);
    // In a real implementation, you would fetch the course data from an API
    if (courseId && courses[courseId]) {
      setCourse(courses[courseId]);
      document.title = `${courses[courseId].title} | ATAC Academy`;
    } else if (courseId === "startup-revolution" && courses["startup-revolution"]) {
      // Handle the case for the startup-revolution route, showing the Startup Revolution course
      setCourse(courses["startup-revolution"]);
      document.title = `${courses["startup-revolution"].title} | ATAC Academy`;
    } else if (courseId === "startup-bootcamp" && courses["startup-bootcamp"]) {
      // Handle the case for the startup-bootcamp route
      setCourse(courses["startup-bootcamp"]);
      document.title = `${courses["startup-bootcamp"].title} | ATAC Academy`;
    } else if (courseId === "ai-na-pratica" && courses["ai-na-pratica"]) {
      // Handle the case for the ai-na-pratica route
      setCourse(courses["ai-na-pratica"]);
      document.title = `${courses["ai-na-pratica"].title} | ATAC Academy`;
    } else {
      // Default to first course if not found
      const firstCourseId = Object.keys(courses)[0];
      if (firstCourseId) {
        setCourse(courses[firstCourseId]);
        document.title = `${courses[firstCourseId].title} | ATAC Academy`;
      }
    }
  }, [courseId]);

  const handleEnrollmentClick = () => {
    setShowPaymentDialog(true);
    
    // Track enrollment click event
    console.log("Enrollment clicked for course:", course?.title);
  };

  const handlePaymentCompletion = () => {
    setShowPaymentDialog(false);
    toast({
      title: "Pagamento concluído!",
      description: `Bem-vindo ao curso ${course?.title}. Redirecionando para o login.`,
      variant: "default",
    });
    
    // Redirect to login page after payment
    setTimeout(() => {
      navigate('/login');
    }, 1500);
  };

  if (!course) {
    return <div>Carregando...</div>;
  }

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      
      {/* Hero Section */}
      <CourseHero course={course} onEnrollmentClick={handleEnrollmentClick} />
      
      {/* Navigation Tabs */}
      <CourseNavTabs activeTab={activeTab} setActiveTab={setActiveTab} course={course}>
        {/* Overview Tab */}
        <TabsContent value="overview" className="pt-12 pb-24">
          <OverviewTab course={course} onEnrollmentClick={handleEnrollmentClick} />
        </TabsContent>
        
        {/* Curriculum Tab */}
        <TabsContent id="curriculum" value="curriculum" className="pt-12 pb-24">
          <CurriculumTab course={course} onEnrollmentClick={handleEnrollmentClick} />
        </TabsContent>
        
        {/* Instructor Tab */}
        <TabsContent value="instructor" className="pt-12 pb-24">
          <InstructorTab course={course} onEnrollmentClick={handleEnrollmentClick} />
        </TabsContent>
        
        {/* FAQ Tab */}
        <TabsContent value="faq" className="pt-12 pb-24">
          <FaqTab course={course} onEnrollmentClick={handleEnrollmentClick} />
        </TabsContent>
      </CourseNavTabs>
      
      {/* Payment Dialog */}
      <PaymentDialog 
        open={showPaymentDialog} 
        onOpenChange={setShowPaymentDialog} 
        onPaymentComplete={handlePaymentCompletion} 
        course={course} 
      />
      
      <Footer />
    </div>
  );
};

export default CourseLanding;
