
import { Card, CardContent } from "@/components/ui/card";
import { 
  Lightbulb, 
  Users, 
  Globe, 
  Activity, 
  TrendingUp, 
  Compass
} from "lucide-react";

const features = [
  {
    icon: Lightbulb,
    title: "Inteligência de Mercado",
    description: "Acesso a relatórios exclusivos e análises de tendências específicas de cada região, mantendo sua empresa à frente das inovações."
  },
  {
    icon: Users,
    title: "Networking Estratégico",
    description: "Conexões com empreendedores, investidores e executivos locais através de eventos exclusivos e encontros personalizados."
  },
  {
    icon: Globe,
    title: "Programas de Imersão",
    description: "Imersões customizadas para executivos em ecossistemas específicos, com foco na cultura de inovação e modelos de negócio."
  },
  {
    icon: Activity,
    title: "Parcerias com Startups",
    description: "Identificação e conexão com startups estratégicas para desenvolvimento de projetos de inovação aberta e cocriação."
  },
  {
    icon: TrendingUp,
    title: "Aceleração de Negócios",
    description: "Programas para expansão internacional de empresas brasileiras com suporte na entrada em novos mercados."
  },
  {
    icon: Compass,
    title: "Exploração Tecnológica",
    description: "Visitas técnicas a centros de inovação, laboratórios e empresas referência em tecnologias emergentes."
  }
];

const HubFeatures = () => {
  return (
    <section className="py-20">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Serviços dos <span className="text-indigo-700">Innovation Hubs</span>
          </h2>
          <p className="text-xl text-gray-700 max-w-3xl mx-auto">
            Conheça os principais serviços oferecidos pela nossa rede de hubs de inovação globais
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <Card key={index} className="border-gray-100 hover:shadow-lg transition-shadow duration-300 overflow-hidden">
              <div className="h-1.5 bg-gradient-to-r from-indigo-500 to-purple-600"></div>
              <CardContent className="pt-8">
                <div className="w-14 h-14 rounded-full bg-indigo-100 flex items-center justify-center mb-6">
                  <feature.icon className="h-7 w-7 text-indigo-700" />
                </div>
                <h3 className="font-montserrat text-xl font-semibold text-gray-900 mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-700">
                  {feature.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default HubFeatures;
