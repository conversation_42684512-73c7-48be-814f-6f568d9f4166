
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";

const CallToAction = () => {
  return (
    <section className="py-20 bg-brand text-white">
      <div className="container mx-auto px-4 text-center">
        <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
          Pronto para uma experiência transformadora?
        </h2>
        <p className="text-xl text-white/80 max-w-3xl mx-auto mb-8">
          Junte-se a executivos e empreendedores de todo o Brasil em uma jornada de aprendizado e conexões globais
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4">
          <Link to="#upcoming">
            <Button size="lg" className="bg-white text-brand hover:bg-gray-100 font-medium px-8">
              Ver <PERSON>
            </Button>
          </Link>
          <Link to="/contato">
            <Button size="lg" className="bg-white text-brand hover:bg-gray-100 font-medium px-8">
              Falar com Consultor
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;
