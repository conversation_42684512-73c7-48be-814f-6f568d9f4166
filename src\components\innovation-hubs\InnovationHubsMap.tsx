
import { useState } from "react";
import { Card } from "@/components/ui/card";
import { MapPin } from "lucide-react";
import { HoverCard, HoverCardTrigger, HoverCardContent } from "@/components/ui/hover-card";

// Hub data
const innovationHubs = [
  {
    id: 1,
    city: "São Francisco",
    country: "Estados Unidos",
    region: "América do Norte",
    position: { left: "15%", top: "38%" },
    specialties: ["AI/ML", "SaaS", "Venture Capital"],
    description: "O epicentro global de tecnologia e inovação, lar das maiores empresas de tecnologia do mundo."
  },
  {
    id: 2,
    city: "Nova York",
    country: "Estados Unidos",
    region: "América do Norte",
    position: { left: "23%", top: "36%" },
    specialties: ["Fintech", "Media", "Retail Tech"],
    description: "Centro de inovação em finanças, mídia e varejo com um ecossistema diversificado de startups."
  },
  {
    id: 3,
    city: "Londres",
    country: "Reino Unido",
    region: "Europa",
    position: { left: "44%", top: "30%" },
    specialties: ["Fintech", "Insurtech", "Creative Tech"],
    description: "Hub europeu de tecnologia financeira e seguros com forte presença de capital de risco."
  },
  {
    id: 4,
    city: "Tel Aviv",
    country: "Israel",
    region: "Oriente Médio",
    position: { left: "53%", top: "42%" },
    specialties: ["Cybersecurity", "Digital Health", "DeepTech"],
    description: "Conhecido como a 'Start-up Nation', é líder mundial em cibersegurança e tecnologias profundas."
  },
  {
    id: 5,
    city: "Shenzhen",
    country: "China",
    region: "Ásia",
    position: { left: "75%", top: "45%" },
    specialties: ["Hardware", "IoT", "Manufacturing"],
    description: "Capital mundial do hardware, manufatura e prototipagem, com ciclos de inovação ultra-rápidos."
  },
  {
    id: 6,
    city: "Singapura",
    country: "Singapura",
    region: "Ásia",
    position: { left: "74%", top: "55%" },
    specialties: ["Smart Cities", "Fintech", "Digital Trade"],
    description: "Centro tecnológico do sudeste asiático com forte apoio governamental e excelente infraestrutura."
  },
  {
    id: 7,
    city: "São Paulo",
    country: "Brasil",
    region: "América do Sul",
    position: { left: "30%", top: "67%" },
    specialties: ["Agritech", "Fintech", "Retail"],
    description: "Maior hub de inovação da América Latina, com um ecossistema crescente de unicórnios e investimentos."
  }
];

const InnovationHubsMap = () => {
  const [activeHub, setActiveHub] = useState<number | null>(null);
  
  const handleHubClick = (hubId: number) => {
    setActiveHub(activeHub === hubId ? null : hubId);
  };
  
  return (
    <div className="relative w-full h-[500px] bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
      <img 
        src="/lovable-uploads/860e495e-d44d-46b7-98f6-e361a25cdfc0.png" 
        alt="World Map"
        className="w-full h-full object-cover"
      />
      
      {/* Hub Pins */}
      {innovationHubs.map((hub) => (
        <div 
          key={hub.id}
          className="absolute z-10 cursor-pointer transform -translate-x-1/2 -translate-y-1/2"
          style={{ left: hub.position.left, top: hub.position.top }}
          onClick={() => handleHubClick(hub.id)}
        >
          <HoverCard openDelay={200} closeDelay={100}>
            <HoverCardTrigger asChild>
              <div className={`
                w-5 h-5 rounded-full bg-indigo-600 border-2 border-white relative
                ${activeHub === hub.id ? 'scale-150' : 'hover:scale-125'}
                transition-all duration-300
              `}>
                <div className={`
                  absolute -top-2 -right-2 w-3 h-3 rounded-full bg-white
                  ${activeHub === hub.id ? 'opacity-100' : 'opacity-0'}
                  transition-opacity duration-300
                `}></div>
              </div>
            </HoverCardTrigger>
            
            <HoverCardContent className="p-0 w-72">
              <Card className="bg-white shadow-lg p-4">
                <div className="flex items-center mb-2">
                  <MapPin className="h-4 w-4 text-indigo-600 mr-1" />
                  <span className="font-semibold">{hub.city}, {hub.country}</span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{hub.description}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {hub.specialties.map((specialty, idx) => (
                    <span 
                      key={idx} 
                      className="bg-indigo-50 text-indigo-700 text-xs px-2 py-1 rounded-full"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </Card>
            </HoverCardContent>
          </HoverCard>
          
          <div className="absolute top-6 left-1/2 transform -translate-x-1/2 whitespace-nowrap">
            <span className="bg-indigo-900/70 text-white text-xs px-2 py-1 rounded font-medium">
              {hub.city}
            </span>
          </div>
        </div>
      ))}
      
      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white/90 p-3 rounded-lg shadow">
        <h4 className="text-sm font-semibold mb-2">Regiões</h4>
        <div className="space-y-1">
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-indigo-600 mr-2"></div>
            <span className="text-xs">Hub Ativo</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 rounded-full bg-gray-400 mr-2"></div>
            <span className="text-xs">Hub em Desenvolvimento</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default InnovationHubsMap;
