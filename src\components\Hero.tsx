
import { CheckCircle } from "lucide-react";
import Hero<PERSON>onte<PERSON> from "./hero/HeroContent";
import HeroBanner from "./hero/HeroBanner";
import StartupCarousel from "./hero/StartupCarousel";

const Hero = ({ 
  smallText = "AVALIAÇÃO 360°",
  title = "Revolucione o seu <span>negócio</span>", 
  description = "Conectamos empreendedores a conhecimento de alto impacto, mentoria especializada e oportunidades de investimento.",
  primaryButtonText = "Comece Agora",
  primaryButtonLink = "/inscrever",
  secondaryButtonText = "Ver Diagnósticos",
  secondaryButtonLink = "/diagnostic",
  stats = [
    { text: "+500 Negócios Acelerados", icon: CheckCircle },
    { text: "R$50M+ Investidos", icon: CheckCircle }
  ],
  showCompanies = false,
  showCarousel = true,
  imageUrl = "",
  className = ""
}) => {
  return (
    <section className={`pt-32 pb-20 bg-white ${className}`}>
      <div className="container mx-auto px-4">
        <div className="flex flex-col lg:flex-row items-center">
          <HeroContent 
            smallText={smallText}
            title={title}
            description={description}
            primaryButtonText={primaryButtonText}
            primaryButtonLink={primaryButtonLink}
            secondaryButtonText={secondaryButtonText}
            secondaryButtonLink={secondaryButtonLink}
            stats={stats}
          />
          <div className="lg:w-1/2 relative">
            <HeroBanner 
              showCarousel={showCarousel} 
              imageUrl={imageUrl} 
            />
          </div>
        </div>
        
        <StartupCarousel showCompanies={showCompanies} />
      </div>
    </section>
  );
};

export default Hero;
