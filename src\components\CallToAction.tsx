
import { Link } from "react-router-dom";
import { ArrowR<PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";

const CallToAction = () => {
  return (
    <section className="py-24 bg-gray-900 text-white relative overflow-hidden">
      {/* Background pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-brand"></div>
        <div className="absolute bottom-12 -left-24 w-64 h-64 rounded-full bg-brand-accent"></div>
      </div>
      
      <div className="container mx-auto px-4 text-center relative z-10">
        <h2 className="font-montserrat text-4xl md:text-5xl font-bold mb-8">
          Pronto para acelerar seu negócio?
        </h2>
        <p className="text-2xl text-white/90 max-w-3xl mx-auto mb-12">
          Junte-se a centenas de empreendedores que estão transformando suas ideias em negócios de sucesso.
        </p>
        <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
          <Link to="/inscricao-programa">
            <Button className="w-full sm:w-auto bg-white text-gray-900 hover:bg-gray-100 px-10 py-7 text-xl font-medium">
              Quero Começar Agora
              <ArrowRight className="ml-2 h-6 w-6" />
            </Button>
          </Link>
          <Link to="/diagnostic">
            <Button className="w-full sm:w-auto bg-white text-gray-900 hover:bg-gray-100 px-10 py-7 text-xl font-medium">
              Ver Diagnósticos
              <ArrowRight className="ml-2 h-6 w-6" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default CallToAction;
