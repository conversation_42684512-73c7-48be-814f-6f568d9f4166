
import { useEffect, useState } from "react";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";

// Hero banner images
const heroBanners = [
  {
    imageUrl: "https://images.unsplash.com/photo-1542744173-8e7e53415bb0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    alt: "Empreendedores em reunião"
  },
  {
    imageUrl: "https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    alt: "Equipe trabalhando em um projeto"
  },
  {
    imageUrl: "https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80",
    alt: "Apresentação de negócios"
  }
];

interface HeroBannerProps {
  showCarousel: boolean;
  imageUrl?: string;
}

const HeroBanner = ({ showCarousel, imageUrl = "" }: HeroBannerProps) => {
  const [autoplayInterval, setAutoplayInterval] = useState<NodeJS.Timeout | null>(null);

  // Autoplay for the carousel
  useEffect(() => {
    if (!showCarousel) return;
    
    const interval = setInterval(() => {
      const nextButton = document.querySelector('.hero-carousel [data-carousel-next]') as HTMLButtonElement;
      if (nextButton) nextButton.click();
    }, 5000);
    
    setAutoplayInterval(interval);
    
    return () => {
      if (autoplayInterval) clearInterval(autoplayInterval);
    };
  }, [showCarousel]);

  if (showCarousel) {
    return (
      <div className="hero-carousel">
        <Carousel className="w-full">
          <CarouselContent>
            {heroBanners.map((banner, index) => (
              <CarouselItem key={index}>
                <div className="relative rounded-xl overflow-hidden shadow-lg">
                  <img 
                    src={banner.imageUrl} 
                    alt={banner.alt} 
                    className="w-full h-auto aspect-video object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-r from-brand/20 to-brand-accent/20 mix-blend-overlay"></div>
                </div>
              </CarouselItem>
            ))}
          </CarouselContent>
          <CarouselPrevious className="hidden md:flex h-10 w-10 bg-white/80 backdrop-blur-sm" />
          <CarouselNext className="hidden md:flex h-10 w-10 bg-white/80 backdrop-blur-sm" />
        </Carousel>
      </div>
    );
  }

  return (
    <div className="relative rounded-xl overflow-hidden shadow-lg">
      <img 
        src={imageUrl} 
        alt="Hero image" 
        className="w-full h-auto aspect-video object-cover"
      />
    </div>
  );
};

export default HeroBanner;
