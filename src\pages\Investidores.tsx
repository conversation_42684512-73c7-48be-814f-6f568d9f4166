
import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  <PERSON>R<PERSON>, 
  TrendingUp, 
  Bar<PERSON>hart, 
  Users, 
  Target, 
  Briefcase, 
  Calendar, 
  LineChart, 
  CheckCircle,
  DollarSign,
  Building,
  Rocket
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import InvestorNetwork from "@/components/InvestorNetwork";

// Investment rounds data
const investmentRounds = [
  {
    title: "Pré-Seed",
    phase: "Ideação",
    startups: 8,
    startDate: "15/01/2024",
    endDate: "15/03/2024",
    status: "active",
    color: "brand-light"
  },
  {
    title: "Seed Capital",
    phase: "Tração Inicial",
    startups: 12,
    startDate: "01/02/2024",
    endDate: "30/04/2024",
    status: "active",
    color: "brand"
  },
  {
    title: "Série A",
    phase: "Expansão",
    startups: 6,
    startDate: "10/03/2024",
    endDate: "10/06/2024",
    status: "upcoming",
    color: "brand-accent"
  },
  {
    title: "Corporate Venture",
    phase: "Escala",
    startups: 4,
    startDate: "01/04/2024",
    endDate: "30/07/2024",
    status: "upcoming",
    color: "enterprise-blue"
  }
];

// Investment statistics
const investmentStats = [
  { metric: "Capital Mobilizado", value: "R$ 88M+", icon: DollarSign },
  { metric: "Startups Investidas", value: "126+", icon: Rocket },
  { metric: "Rodadas Finalizadas", value: "42", icon: Briefcase },
  { metric: "Taxa de Sucesso", value: "73%", icon: BarChart }
];

const Investidores = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Rede de Investidores | ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-20">
        {/* Hero Section */}
        <section className="relative py-20 mb-20 overflow-hidden">
          {/* Background elements */}
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white z-0"></div>
          <div className="absolute top-0 right-0 w-full h-full opacity-10 z-0">
            <div className="absolute top-0 right-0 w-2/3 h-full bg-gradient-to-bl from-brand/20 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-brand-accent/20 to-transparent"></div>
          </div>
          
          <div className="container mx-auto px-4 relative z-10">
            <div className="flex flex-col md:flex-row items-center">
              <div className="md:w-1/2 mb-10 md:mb-0 md:pr-12">
                <div className="inline-flex items-center px-3 py-1 rounded-full bg-brand-light text-brand font-medium text-sm mb-6">
                  <TrendingUp className="w-4 h-4 mr-2" /> Portal do Investidor
                </div>
                <h1 className="font-montserrat text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                  Invista em startups que <span className="text-brand">transformam</span> o futuro
                </h1>
                <p className="text-xl text-gray-700 mb-8 leading-relaxed">
                  Acesso exclusivo a rodadas de investimento em startups inovadoras, 
                  com acompanhamento especializado e networking estratégico.
                </p>
                <div className="flex flex-wrap gap-4 mb-8">
                  <Button size="lg">
                    Acessar Portal do Investidor
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                  <Button size="lg" variant="outline" className="border-brand text-brand hover:bg-brand-light/50">
                    Conhecer Startups
                  </Button>
                </div>
                
                <div className="flex flex-wrap items-center gap-4 md:gap-8">
                  <div className="flex items-center">
                    <CheckCircle className="text-brand mr-2 h-5 w-5" />
                    <span className="text-gray-700">Due Diligence Completa</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="text-brand mr-2 h-5 w-5" />
                    <span className="text-gray-700">Acesso Prioritário</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="text-brand mr-2 h-5 w-5" />
                    <span className="text-gray-700">Deals Exclusivos</span>
                  </div>
                </div>
              </div>
              
              <div className="md:w-1/2 relative">
                <div className="bg-white rounded-lg shadow-xl overflow-hidden p-6 border border-gray-100">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="font-bold text-xl text-gray-900">Dashboard do Investidor</h3>
                    <Button variant="ghost" className="text-brand hover:text-brand-secondary hover:bg-brand-light/50">
                      Ver todas
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </div>
                  
                  <h4 className="text-lg font-semibold text-gray-800 mb-4">Rodadas em andamento</h4>
                  <div className="space-y-4 mb-8">
                    {investmentRounds.filter(round => round.status === 'active').map((round, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-100 hover:border-brand/30 transition-colors">
                        <div className="flex items-start space-x-3">
                          <div className={`w-10 h-10 rounded-full bg-${round.color} flex items-center justify-center shrink-0`}>
                            <TrendingUp className="h-5 w-5 text-white" />
                          </div>
                          <div>
                            <h5 className="font-semibold text-gray-900">{round.title}</h5>
                            <p className="text-sm text-gray-600">{round.phase} • {round.startups} startups</p>
                          </div>
                        </div>
                        <div className="text-right text-sm">
                          <p className="text-gray-600">Prazo: {round.endDate}</p>
                          <Button variant="link" className="h-8 px-0 text-brand">
                            Participar <ArrowRight className="ml-1 h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div className="pt-4 border-t border-gray-100">
                    <p className="text-sm text-gray-500 mb-3">Próximas rodadas</p>
                    <div className="grid grid-cols-2 gap-3">
                      {investmentRounds.filter(round => round.status === 'upcoming').map((round, index) => (
                        <div key={index} className="p-3 rounded-lg bg-gray-50 border border-gray-100">
                          <p className="font-medium text-gray-900">{round.title}</p>
                          <p className="text-sm text-gray-600 mb-2">{round.phase}</p>
                          <div className="flex items-center text-xs text-gray-500">
                            <Calendar className="h-3 w-3 mr-1" />
                            Início: {round.startDate}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Statistics Section */}
        <section className="py-16 mb-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Impacto da nossa <span className="text-brand">Rede de Investidores</span>
              </h2>
              <p className="text-xl text-gray-700 max-w-3xl mx-auto">
                Conectamos capital inteligente a startups inovadoras, gerando valor para o ecossistema de inovação brasileiro.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {investmentStats.map((stat, index) => (
                <div key={index} className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 hover:border-brand/30 transition-all hover:shadow-md">
                  <div className={`w-14 h-14 rounded-full bg-brand-light flex items-center justify-center mb-6`}>
                    <stat.icon className="h-7 w-7 text-brand" />
                  </div>
                  <h3 className="text-4xl font-bold text-gray-900 mb-2">{stat.value}</h3>
                  <p className="text-gray-600">{stat.metric}</p>
                </div>
              ))}
            </div>
          </div>
        </section>
        
        {/* How It Works Section */}
        <section className="py-16 mb-20">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-16">
              <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Como funciona nossa plataforma
              </h2>
              <p className="text-xl text-gray-700">
                Um processo simplificado e transparente para conectar investidores a oportunidades de alto potencial.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-10">
              <div className="relative">
                <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 h-full">
                  <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center mb-6 relative z-10">
                    <span className="text-3xl font-bold text-brand">1</span>
                  </div>
                  <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-4">
                    Cadastro e Qualificação
                  </h3>
                  <p className="text-gray-700">
                    Faça seu cadastro como investidor e passe por um processo de qualificação 
                    para acessar deals exclusivos na plataforma.
                  </p>
                </div>
                <div className="hidden md:block absolute top-1/2 left-full -translate-y-1/2 w-16 h-2 bg-gray-200 -translate-x-8 z-0"></div>
              </div>
              
              <div className="relative">
                <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 h-full">
                  <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center mb-6 relative z-10">
                    <span className="text-3xl font-bold text-brand">2</span>
                  </div>
                  <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-4">
                    Explore Oportunidades
                  </h3>
                  <p className="text-gray-700">
                    Acesse rodadas de investimento cuidadosamente selecionadas, 
                    com toda documentação e análise necessária para sua decisão.
                  </p>
                </div>
                <div className="hidden md:block absolute top-1/2 left-full -translate-y-1/2 w-16 h-2 bg-gray-200 -translate-x-8 z-0"></div>
              </div>
              
              <div>
                <div className="bg-white rounded-xl p-8 shadow-sm border border-gray-100 h-full">
                  <div className="w-16 h-16 rounded-full bg-brand-light flex items-center justify-center mb-6">
                    <span className="text-3xl font-bold text-brand">3</span>
                  </div>
                  <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-4">
                    Invista e Acompanhe
                  </h3>
                  <p className="text-gray-700">
                    Realize seus investimentos pela plataforma e acompanhe o desempenho 
                    das startups com relatórios detalhados e reuniões periódicas.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        {/* Investment Opportunities */}
        <section className="py-16 mb-20 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row items-center justify-between mb-16">
              <div className="mb-8 md:mb-0 md:max-w-2xl">
                <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                  Oportunidades de investimento em startups de alto potencial
                </h2>
                <p className="text-xl text-gray-700">
                  Startups validadas e com modelos de negócio escaláveis, prontas para receber investimento.
                </p>
              </div>
              <Button>
                Ver Todas as Oportunidades
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Investment Opportunity Cards */}
              <Card className="overflow-hidden transition-all hover:shadow-md border-transparent hover:border-brand/30">
                <div className="relative h-48 bg-gray-100">
                  <div className="absolute top-4 left-4 bg-brand-accent text-white px-3 py-1 rounded-full text-sm font-medium">
                    Seed Capital
                  </div>
                  <img 
                    src="https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                    alt="Fintech startup" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle>FinFlow</CardTitle>
                    <div className="text-sm font-medium text-brand-accent">
                      Fintech
                    </div>
                  </div>
                  <CardDescription>Plataforma de gestão financeira para PMEs</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Captação</span>
                      <span className="font-medium">R$ 2,5M</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Ticket mínimo</span>
                      <span className="font-medium">R$ 100K</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Valoração</span>
                      <span className="font-medium">R$ 12M</span>
                    </div>
                    <div className="pt-4">
                      <Button variant="outline" className="w-full border-brand text-brand hover:bg-brand-light/50">
                        Ver Detalhes
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="overflow-hidden transition-all hover:shadow-md border-transparent hover:border-brand/30">
                <div className="relative h-48 bg-gray-100">
                  <div className="absolute top-4 left-4 bg-brand text-white px-3 py-1 rounded-full text-sm font-medium">
                    Série A
                  </div>
                  <img 
                    src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                    alt="Healthtech startup" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle>HealthConnect</CardTitle>
                    <div className="text-sm font-medium text-brand">
                      Healthtech
                    </div>
                  </div>
                  <CardDescription>Telemedicina e gestão de saúde digital</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Captação</span>
                      <span className="font-medium">R$ 8M</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Ticket mínimo</span>
                      <span className="font-medium">R$ 250K</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Valoração</span>
                      <span className="font-medium">R$ 42M</span>
                    </div>
                    <div className="pt-4">
                      <Button variant="outline" className="w-full border-brand text-brand hover:bg-brand-light/50">
                        Ver Detalhes
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
              
              <Card className="overflow-hidden transition-all hover:shadow-md border-transparent hover:border-brand/30">
                <div className="relative h-48 bg-gray-100">
                  <div className="absolute top-4 left-4 bg-enterprise-blue text-white px-3 py-1 rounded-full text-sm font-medium">
                    Pré-Seed
                  </div>
                  <img 
                    src="https://images.unsplash.com/photo-1620712943543-bcc4688e7485?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80" 
                    alt="Edtech startup" 
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-center">
                    <CardTitle>EduTech</CardTitle>
                    <div className="text-sm font-medium text-enterprise-blue">
                      Edtech
                    </div>
                  </div>
                  <CardDescription>Plataforma de aprendizado personalizado</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Captação</span>
                      <span className="font-medium">R$ 1,2M</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Ticket mínimo</span>
                      <span className="font-medium">R$ 50K</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Valoração</span>
                      <span className="font-medium">R$ 6M</span>
                    </div>
                    <div className="pt-4">
                      <Button variant="outline" className="w-full border-brand text-brand hover:bg-brand-light/50">
                        Ver Detalhes
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>
        
        {/* Investor Network Section */}
        <InvestorNetwork 
          smallText="REDE EXCLUSIVA"
          title="Faça parte da nossa <span>Comunidade de</span> <span class='text-brand-accent'>Investidores</span>"
          description="Uma rede selecionada de investidores anjos, fundos de venture capital e corporate ventures que buscam oportunidades de alto potencial no mercado brasileiro."
          primaryButtonText="Quero me tornar um investidor"
          primaryButtonLink="/contato"
          secondaryButtonText="Conhecer os membros"
          secondaryButtonLink="/investidores/rede"
          statValue="230+"
          statText="Investidores ativos"
          features={[
            {
              icon: Target,
              title: "Deals Exclusivos",
              description: "Acesso prioritário a startups selecionadas"
            },
            {
              icon: BarChart,
              title: "Due Diligence Completa",
              description: "Análises detalhadas de cada oportunidade"
            },
            {
              icon: Users,
              title: "Coinvestimento",
              description: "Possibilidade de coinvestir com outros membros"
            },
            {
              icon: Building,
              title: "Corporate Ventures",
              description: "Conexão com programas de inovação corporativa"
            }
          ]}
        />
        
        {/* Portal Access CTA */}
        <section className="py-20 bg-gradient-to-r from-brand to-brand-accent text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
              Pronto para investir em startups inovadoras?
            </h2>
            <p className="text-xl mb-10 max-w-2xl mx-auto text-white/90">
              Acesse nossa plataforma e descubra como investir em startups de alto potencial 
              de forma segura e estruturada.
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button size="lg" className="bg-white text-brand hover:bg-gray-100">
                Acessar Portal do Investidor
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" className="bg-white text-brand hover:bg-gray-100">
                Agendar Demonstração
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default Investidores;
