
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import PurchaseAssistant from "@/components/PurchaseAssistant";

interface CourseNavTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  children: React.ReactNode;
  course: any;
}

const CourseNavTabs = ({ activeTab, setActiveTab, children, course }: CourseNavTabsProps) => {
  return (
    <div className="bg-white sticky top-16 z-30 border-b shadow-sm">
      <div className="container mx-auto px-4">
        <Tabs defaultValue="overview" className="w-full" onValueChange={setActiveTab}>
          <div className="overflow-x-auto">
            <TabsList className="bg-transparent h-16 border-b-0 justify-start space-x-8">
              <TabsTrigger value="overview" className="data-[state=active]:border-b-2 data-[state=active]:border-brand data-[state=active]:shadow-none rounded-none bg-transparent h-16 text-base font-medium">
                V<PERSON><PERSON>
              </TabsTrigger>
              <TabsTrigger value="curriculum" className="data-[state=active]:border-b-2 data-[state=active]:border-brand data-[state=active]:shadow-none rounded-none bg-transparent h-16 text-base font-medium">
                Programa
              </TabsTrigger>
              <TabsTrigger value="instructor" className="data-[state=active]:border-b-2 data-[state=active]:border-brand data-[state=active]:shadow-none rounded-none bg-transparent h-16 text-base font-medium">
                Instrutor
              </TabsTrigger>
              <TabsTrigger value="faq" className="data-[state=active]:border-b-2 data-[state=active]:border-brand data-[state=active]:shadow-none rounded-none bg-transparent h-16 text-base font-medium">
                FAQ
              </TabsTrigger>
            </TabsList>
          </div>
          {children}
        </Tabs>

        {/* Mobile Purchase Assistant */}
        <div className="lg:hidden mt-4 mb-8">
          <PurchaseAssistant 
            productName={course.title} 
            productType={course.type}
          />
        </div>
      </div>
    </div>
  );
};

export default CourseNavTabs;
