
import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Users, Award, Briefcase } from "lucide-react";
import { mentors } from "@/data/mentors";

// Import new components
import ProfileHeader from "@/components/mentor-profile/ProfileHeader";
import ProfileTabs from "@/components/mentor-profile/ProfileTabs";
import BookingWidget from "@/components/mentor-profile/BookingWidget";
import RelatedMentors from "@/components/mentor-profile/RelatedMentors";
import MentorNotFound from "@/components/mentor-profile/MentorNotFound";

const MentorProfile = () => {
  const { id } = useParams();
  const [mentor, setMentor] = useState<any>(null);
  
  useEffect(() => {
    window.scrollTo(0, 0);
    
    // Find the mentor based on the id from URL params
    const foundMentor = mentors.find(m => m.id.toString() === id);
    if (foundMentor) {
      setMentor(foundMentor);
      document.title = `${foundMentor.name} | Mentor ATAC Academy`;
    }
  }, [id]);

  if (!mentor) {
    return (
      <div className="min-h-screen bg-white">
        <Navbar />
        <main className="pt-24 pb-20">
          <div className="container mx-auto px-4">
            <MentorNotFound />
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Generate the data needed for components
  // Available time slots for scheduling
  const availableTimeSlots = [
    "09:00", "10:00", "11:00", "14:00", "15:00", "16:00", "17:00"
  ];

  // Calculate dates for the next 14 days
  const getNextTwoWeeksDates = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 1; i <= 14; i++) {
      const date = new Date();
      date.setDate(today.getDate() + i);
      
      // Only include dates that match mentor's availability
      const dayName = date.toLocaleDateString('pt-BR', { weekday: 'long' });
      const shortDayName = dayName.charAt(0).toUpperCase() + dayName.slice(1, 5);
      
      if (mentor.availability.some((day: string) => day === shortDayName || 
          day.toLowerCase().includes(shortDayName.toLowerCase()))) {
        dates.push({
          date: date,
          dayName: dayName,
          formattedDate: date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })
        });
      }
    }
    
    return dates;
  };

  const availableDates = getNextTwoWeeksDates();

  const achievements = [
    {
      title: `Mais de ${mentor.sessions} mentorias realizadas`,
      icon: <Users className="h-5 w-5 text-brand" />
    },
    {
      title: `Especialista em ${mentor.expertise[0]}`,
      icon: <Award className="h-5 w-5 text-brand" />
    },
    {
      title: `${5 + mentor.sessions % 5} anos de experiência em ${mentor.expertise[1] || 'mercado'}`,
      icon: <Briefcase className="h-5 w-5 text-brand" />
    }
  ];

  const benefits = [
    "Mentorias personalizadas para o seu negócio",
    "Feedback sincero e direcionado",
    "Networking com outros empreendedores",
    "Acesso a materiais e recursos exclusivos"
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-20 pb-20">
        {/* Hero Section with Profile Header */}
        <ProfileHeader mentor={mentor} achievements={achievements} />
        
        <div className="container mx-auto px-4 py-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="md:col-span-2">
              <ProfileTabs mentor={mentor} benefits={benefits} />
            </div>
            
            {/* Booking Section */}
            <div className="md:col-span-1">
              <BookingWidget 
                mentor={mentor} 
                availableDates={availableDates} 
                availableTimeSlots={availableTimeSlots}
              />
            </div>
          </div>
        </div>
        
        {/* Related Mentors Section */}
        <RelatedMentors 
          currentMentorId={mentor.id} 
          mentors={mentors} 
          expertise={mentor.expertise}
        />
      </main>
      <Footer />
    </div>
  );
};

export default MentorProfile;
