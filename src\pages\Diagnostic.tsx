import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import PurchaseAssistant from "@/components/PurchaseAssistant";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowRight,
  BarChart2,
  FileSpreadsheet,
  CheckCircle,
  Lock,
  Unlock,
  ShoppingCart,
  CreditCard,
  Lightbulb,
  TrendingUp,
  Search,
  Info,
  PlayCircle,
  Download,
  Calendar,
  MessageSquare,
  Rocket,
  Building,
  Globe,
  Users,
  Target,
  Star,
  Clock,
  Award,
  Zap,
  Brain,
  Shield
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/components/ui/use-toast";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const pricingDetails = {
  valuation: {
    price: 1297,
    title: "Diagnóstico de Valuation",
    description: "Descubra o valor estimado do seu negócio com nossa ferramenta de diagnóstico baseada em métricas de mercado e indicadores financeiros.",
    duration: "45-60 min",
    deliveryTime: "24-48h",
    icon: BarChart2,
    color: "brand",
    features: [
      "Análise de indicadores financeiros",
      "Comparativo com mercado",
      "Projeção de crescimento",
      "Relatório detalhado em PDF",
      "30 minutos de mentoria pós-diagnóstico",
      "Dashboard interativo de métricas",
      "Recomendações de melhoria"
    ],
    benefits: [
      "Prepare-se para captação de investimento",
      "Entenda o valor real do seu negócio",
      "Identifique drivers de valor",
      "Compare-se com o mercado"
    ]
  },
  maturity: {
    price: 347,
    title: "Assessment de Maturidade",
    description: "Identifique o nível de maturidade da sua startup e receba recomendações personalizadas para avançar para o próximo estágio.",
    duration: "30-45 min",
    deliveryTime: "12-24h",
    icon: FileSpreadsheet,
    color: "brand-accent",
    features: [
      "Análise de processo e operações",
      "Avaliação de modelo de negócio",
      "Estratégia de crescimento",
      "Relatório detalhado em PDF",
      "30 minutos de mentoria pós-diagnóstico",
      "Roadmap de evolução personalizado",
      "Benchmarking com startups similares"
    ],
    benefits: [
      "Identifique gargalos operacionais",
      "Receba roadmap de crescimento",
      "Compare-se com startups do seu setor",
      "Otimize processos internos"
    ]
  },
  bundle: {
    price: Math.round(((1297 + 347) * 0.8)),
    discount: 20,
    title: "Pacote Completo",
    description: "Adquira ambos os diagnósticos por um preço especial e tenha uma visão completa do seu negócio.",
    duration: "75-105 min",
    deliveryTime: "48-72h",
    icon: Rocket,
    color: "gradient",
    features: [
      "Todos os benefícios do Diagnóstico de Valuation",
      "Todos os benefícios do Assessment de Maturidade",
      "60 minutos de mentoria pós-diagnóstico",
      "Acesso a comunidade exclusiva por 3 meses",
      "Desconto de 20% em relação à compra individual",
      "Plano de ação integrado",
      "Acompanhamento mensal por 3 meses"
    ],
    benefits: [
      "Visão 360° do seu negócio",
      "Estratégia integrada de crescimento",
      "Acompanhamento especializado",
      "Acesso à rede de empreendedores"
    ]
  }
};

const Diagnostic = () => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [isCheckout, setIsCheckout] = useState(false);
  const { toast } = useToast();
  const navigate = useNavigate();

  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Diagnóstico | ATAC Academy";
  }, []);

  const handleProceedToCheckout = () => {
    if (!selectedPlan) {
      toast({
        title: "Selecione um plano",
        description: "Por favor, selecione um plano para continuar.",
        variant: "destructive",
      });
      return;
    }
    
    setIsCheckout(true);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleFinishPurchase = () => {
    toast({
      title: "Compra realizada com sucesso!",
      description: "Você receberá um email com os detalhes de acesso ao(s) diagnóstico(s).",
    });
    
    setTimeout(() => {
      if (selectedPlan === "valuation") {
        navigate("/valuation");
      } else if (selectedPlan === "maturity") {
        navigate("/maturity");
      } else if (selectedPlan === "bundle") {
        navigate("/valuation");
      } else {
        navigate("/diagnostic");
      }
    }, 2000);
  };

  if (isCheckout) {
    const selectedPackage = selectedPlan === "bundle" 
      ? pricingDetails.bundle 
      : selectedPlan === "maturity" 
        ? pricingDetails.maturity 
        : pricingDetails.valuation;

    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <main className="pt-28 pb-20">
          <div className="container mx-auto px-4 max-w-4xl">
            <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
              <div className="flex justify-between items-center mb-8">
                <h1 className="font-montserrat text-2xl md:text-3xl font-bold text-gray-900">
                  Finalizar compra
                </h1>
                <Button 
                  variant="ghost" 
                  onClick={() => setIsCheckout(false)}
                  className="text-gray-500"
                >
                  Voltar
                </Button>
              </div>
              
              <div className="mb-8 p-6 bg-gray-50 rounded-lg">
                <h2 className="font-semibold text-lg mb-4">Resumo do pedido</h2>
                <div className="flex justify-between items-center mb-2">
                  <span>{selectedPackage.title}</span>
                  <span className="font-medium">R$ {selectedPackage.price.toFixed(2)}</span>
                </div>
                {selectedPlan === "bundle" && (
                  <div className="text-sm text-green-600 mb-4">
                    Economia de R$ {(pricingDetails.valuation.price + pricingDetails.maturity.price - pricingDetails.bundle.price).toFixed(2)} (15% de desconto)
                  </div>
                )}
                <div className="border-t border-gray-200 my-4 pt-4 flex justify-between font-bold">
                  <span>Total</span>
                  <span>R$ {selectedPackage.price.toFixed(2)}</span>
                </div>
              </div>
              
              <div className="space-y-6">
                <h2 className="font-semibold text-lg">Dados de pagamento</h2>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Nome no cartão</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="Nome no cartão"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Número do cartão</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="1234 5678 9012 3456"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">Data de validade</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="MM/AA"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <label className="block text-sm font-medium text-gray-700">CVV</label>
                    <input 
                      type="text" 
                      className="w-full px-4 py-3 rounded-md border border-gray-300 focus:outline-none focus:ring-2 focus:ring-brand"
                      placeholder="123"
                    />
                  </div>
                </div>
                
                <div className="pt-6 mt-6 border-t border-gray-200">
                  <Button 
                    onClick={handleFinishPurchase}
                    className="w-full py-6 text-lg bg-brand hover:bg-brand-secondary flex items-center justify-center"
                  >
                    <CreditCard className="mr-2 h-5 w-5" />
                    Finalizar Pagamento
                  </Button>
                  <p className="text-center text-sm text-gray-500 mt-4">
                    Seus dados estão protegidos com criptografia de ponta a ponta
                  </p>
                </div>
              </div>
            </div>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      <main className="pt-28 pb-20">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-16">
            <h1 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              Diagnóstico <span className="text-brand">Empresarial</span> Inteligente
            </h1>
            <p className="text-xl text-gray-700 mb-8">
              Avalie o estágio do seu negócio e descubra oportunidades estratégicas para aceleração e crescimento com nossa metodologia comprovada.
            </p>

            {/* Stats Section */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-3xl font-bold text-brand mb-2">500+</div>
                <div className="text-gray-600">Empresas analisadas</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-3xl font-bold text-brand-accent mb-2">95%</div>
                <div className="text-gray-600">Taxa de satisfação</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-sm border border-gray-100">
                <div className="text-3xl font-bold text-green-600 mb-2">R$ 2.5M</div>
                <div className="text-gray-600">Valor médio captado</div>
              </div>
            </div>

            {/* Demo Video Section */}
            <div className="bg-white rounded-xl shadow-lg p-8 mb-12">
              <h3 className="text-2xl font-bold mb-4">Veja como funciona</h3>
              <div className="relative bg-gray-100 rounded-lg aspect-video flex items-center justify-center mb-4">
                <PlayCircle className="h-16 w-16 text-brand cursor-pointer hover:text-brand-secondary transition-colors" />
              </div>
              <p className="text-gray-600">
                Assista a uma demonstração de 3 minutos sobre como nossos diagnósticos podem transformar sua estratégia empresarial.
              </p>
            </div>
          </div>
          
          <div className="max-w-5xl mx-auto mb-16 bg-white rounded-xl shadow-md overflow-hidden">
            <div className="p-8 md:p-12">
              <h2 className="text-2xl md:text-3xl font-bold text-gray-900 mb-6">
                Por que realizar um diagnóstico empresarial?
              </h2>
              
              <p className="text-gray-700 mb-8 text-lg">
                Um diagnóstico preciso é o primeiro passo para tomar decisões estratégicas 
                que impulsionam o crescimento da sua empresa. Nossos diagnósticos ajudam 
                empreendedores e gestores a identificar gargalos, potencializar forças e 
                planejar ações concretas para o futuro.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="flex items-start">
                  <div className="bg-brand/10 p-3 rounded-full mr-4">
                    <Search className="h-6 w-6 text-brand" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Identifique oportunidades</h3>
                    <p className="text-gray-600">
                      Descubra áreas inexploradas e oportunidades de crescimento com base em dados concretos.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-brand-accent/10 p-3 rounded-full mr-4">
                    <BarChart2 className="h-6 w-6 text-brand-accent" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Decisões baseadas em dados</h3>
                    <p className="text-gray-600">
                      Substitua intuição por métricas e indicadores que realmente mostram o desempenho do seu negócio.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-green-100 p-3 rounded-full mr-4">
                    <TrendingUp className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Impulsione seu crescimento</h3>
                    <p className="text-gray-600">
                      Receba estratégias e planos de ação personalizados para acelerar o crescimento da sua empresa.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="bg-blue-100 p-3 rounded-full mr-4">
                    <Info className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-bold text-lg mb-2">Acompanhamento especializado</h3>
                    <p className="text-gray-600">
                      Conte com a mentoria de especialistas para interpretar resultados e definir os próximos passos.
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                <p className="text-lg font-medium text-gray-900 mb-2">
                  Mais de 300 empreendedores já utilizaram nossos diagnósticos para:
                </p>
                <ul className="grid grid-cols-1 md:grid-cols-2 gap-3 text-gray-700">
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Captar investimentos com maior facilidade
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Aumentar o valor de mercado da empresa
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Identificar gargalos operacionais
                  </li>
                  <li className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" /> Otimizar processos e reduzir custos
                  </li>
                </ul>
              </div>
            </div>
          </div>
          
          <div className="max-w-6xl mx-auto mb-20">
            <h2 className="text-3xl font-bold text-center mb-12">Escolha seu diagnóstico</h2>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Valuation Diagnostic */}
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl ${selectedPlan === 'valuation' ? 'ring-4 ring-brand transform scale-105' : ''}`}>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-brand/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <BarChart2 className="h-8 w-8 text-brand" />
                    </div>
                    <div className="text-right">
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.valuation.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                    </div>
                  </div>

                  <h2 className="text-2xl font-bold mb-2">{pricingDetails.valuation.title}</h2>

                  {/* Duration and Delivery Info */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {pricingDetails.valuation.duration}
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {pricingDetails.valuation.deliveryTime}
                    </div>
                  </div>

                  <p className="text-gray-700 mb-6">
                    {pricingDetails.valuation.description}
                  </p>

                  {/* Benefits */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3 text-brand">Principais benefícios:</h4>
                    <div className="space-y-2">
                      {pricingDetails.valuation.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-start">
                          <Star className="h-4 w-4 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-sm text-gray-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <h4 className="font-semibold">O que está incluído:</h4>
                    {pricingDetails.valuation.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-brand mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    onClick={() => setSelectedPlan(selectedPlan === 'valuation' ? null : 'valuation')}
                    className={`w-full py-6 ${selectedPlan === 'valuation' ? 'bg-brand hover:bg-brand-secondary' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                  >
                    {selectedPlan === 'valuation' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Lock className="mr-2 h-5 w-5" />
                        Selecionar
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Maturity Assessment */}
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl ${selectedPlan === 'maturity' ? 'ring-4 ring-brand-accent transform scale-105' : ''}`}>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-brand-accent/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <FileSpreadsheet className="h-8 w-8 text-brand-accent" />
                    </div>
                    <div className="text-right">
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.maturity.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                    </div>
                  </div>

                  <h2 className="text-2xl font-bold mb-2">{pricingDetails.maturity.title}</h2>

                  {/* Duration and Delivery Info */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {pricingDetails.maturity.duration}
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {pricingDetails.maturity.deliveryTime}
                    </div>
                  </div>

                  <p className="text-gray-700 mb-6">
                    {pricingDetails.maturity.description}
                  </p>

                  {/* Benefits */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3 text-brand-accent">Principais benefícios:</h4>
                    <div className="space-y-2">
                      {pricingDetails.maturity.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-start">
                          <Star className="h-4 w-4 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-sm text-gray-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <h4 className="font-semibold">O que está incluído:</h4>
                    {pricingDetails.maturity.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-brand-accent mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    onClick={() => setSelectedPlan(selectedPlan === 'maturity' ? null : 'maturity')}
                    className={`w-full py-6 ${selectedPlan === 'maturity' ? 'bg-brand-accent hover:bg-brand-accent/90' : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}`}
                  >
                    {selectedPlan === 'maturity' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Lock className="mr-2 h-5 w-5" />
                        Selecionar
                      </>
                    )}
                  </Button>
                </div>
              </div>
              
              {/* Complete Bundle - Most Popular */}
              <div className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 hover:shadow-xl relative ${selectedPlan === 'bundle' ? 'ring-4 ring-brand transform scale-105' : ''}`}>
                <div className="bg-gradient-to-r from-brand to-brand-accent text-white p-3 text-center">
                  <span className="font-medium flex items-center justify-center">
                    <Award className="h-4 w-4 mr-2" />
                    Mais Popular - Melhor custo-benefício
                  </span>
                </div>
                <div className="p-8">
                  <div className="flex justify-between items-start mb-6">
                    <div className="bg-gradient-to-br from-brand/10 to-brand-accent/10 p-4 rounded-full w-16 h-16 flex items-center justify-center">
                      <Rocket className="h-8 w-8 text-brand" />
                    </div>
                    <div className="text-right">
                      <div className="flex items-center justify-end mb-1">
                        <span className="line-through text-gray-400 mr-2 text-lg">R$ {(pricingDetails.valuation.price + pricingDetails.maturity.price).toFixed(0)}</span>
                        <Badge className="bg-green-600">-{pricingDetails.bundle.discount}%</Badge>
                      </div>
                      <span className="block text-3xl font-bold text-gray-900">R$ {pricingDetails.bundle.price}</span>
                      <span className="text-gray-500">pagamento único</span>
                      <div className="text-sm text-green-600 font-medium mt-1">
                        Economia de R$ {(pricingDetails.valuation.price + pricingDetails.maturity.price - pricingDetails.bundle.price).toFixed(0)}
                      </div>
                    </div>
                  </div>

                  <h2 className="text-2xl font-bold mb-2">{pricingDetails.bundle.title}</h2>

                  {/* Duration and Delivery Info */}
                  <div className="flex items-center gap-4 mb-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {pricingDetails.bundle.duration}
                    </div>
                    <div className="flex items-center">
                      <Download className="h-4 w-4 mr-1" />
                      {pricingDetails.bundle.deliveryTime}
                    </div>
                  </div>

                  <p className="text-gray-700 mb-6">
                    {pricingDetails.bundle.description}
                  </p>

                  {/* Benefits */}
                  <div className="mb-6">
                    <h4 className="font-semibold mb-3 text-brand">Principais benefícios:</h4>
                    <div className="space-y-2">
                      {pricingDetails.bundle.benefits.map((benefit, index) => (
                        <div key={index} className="flex items-start">
                          <Star className="h-4 w-4 text-yellow-500 mr-2 flex-shrink-0 mt-0.5" />
                          <span className="text-sm text-gray-700">{benefit}</span>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    <h4 className="font-semibold">O que está incluído:</h4>
                    {pricingDetails.bundle.features.map((feature, index) => (
                      <div key={index} className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-3 flex-shrink-0 mt-0.5" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <Button
                    onClick={() => setSelectedPlan(selectedPlan === 'bundle' ? null : 'bundle')}
                    className={`w-full py-6 ${selectedPlan === 'bundle' ? 'bg-gradient-to-r from-brand to-brand-accent hover:from-brand/90 hover:to-brand-accent/90' : 'bg-gradient-to-r from-brand to-brand-accent hover:from-brand/90 hover:to-brand-accent/90 text-white'}`}
                  >
                    {selectedPlan === 'bundle' ? (
                      <>
                        <CheckCircle className="mr-2 h-5 w-5" />
                        Selecionado
                      </>
                    ) : (
                      <>
                        <Rocket className="mr-2 h-5 w-5" />
                        Escolher Pacote Completo
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
            
            <div className="mt-12 text-center">
              <Button
                onClick={handleProceedToCheckout}
                className="bg-gray-900 hover:bg-gray-800 text-white py-6 px-10 text-lg"
                disabled={!selectedPlan}
              >
                <ShoppingCart className="mr-2 h-5 w-5" />
                Prosseguir para pagamento
              </Button>
              <p className="text-gray-500 mt-4">Pagamento 100% seguro e protegido</p>
            </div>
          </div>
          
          {/* Testimonials Section */}
          <div className="max-w-6xl mx-auto mt-20 mb-16">
            <h2 className="text-3xl font-bold text-center mb-12">O que nossos clientes dizem</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <Card className="bg-white">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-500">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">
                    "O diagnóstico de valuation foi fundamental para nossa captação. Conseguimos R$ 2.5M em investimento usando as recomendações do relatório."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-brand/10 rounded-full flex items-center justify-center mr-3">
                      <Users className="h-5 w-5 text-brand" />
                    </div>
                    <div>
                      <div className="font-semibold">Carlos Silva</div>
                      <div className="text-sm text-gray-600">CEO, TechStart</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-500">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">
                    "O assessment de maturidade nos ajudou a identificar gargalos que nem sabíamos que existiam. Aumentamos nossa eficiência em 40%."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-brand-accent/10 rounded-full flex items-center justify-center mr-3">
                      <Building className="h-5 w-5 text-brand-accent" />
                    </div>
                    <div>
                      <div className="font-semibold">Ana Costa</div>
                      <div className="text-sm text-gray-600">Fundadora, EcoSolutions</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <div className="flex items-center mb-4">
                    <div className="flex text-yellow-500">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-current" />
                      ))}
                    </div>
                  </div>
                  <p className="text-gray-700 mb-4">
                    "O pacote completo transformou nossa estratégia. A mentoria pós-diagnóstico foi o diferencial que precisávamos."
                  </p>
                  <div className="flex items-center">
                    <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center mr-3">
                      <Rocket className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <div className="font-semibold">Roberto Lima</div>
                      <div className="text-sm text-gray-600">CTO, FinanceApp</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* FAQ Section */}
          <div className="max-w-4xl mx-auto mt-20">
            <h2 className="text-3xl font-bold text-center mb-12">Perguntas frequentes</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <MessageSquare className="h-5 w-5 mr-2 text-brand" />
                    Como funcionam os diagnósticos?
                  </h3>
                  <p className="text-gray-700">Após a compra, você terá acesso à plataforma onde poderá preencher os formulários de diagnóstico. Depois de concluído, você receberá um relatório detalhado com recomendações personalizadas.</p>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <Clock className="h-5 w-5 mr-2 text-brand-accent" />
                    Quanto tempo leva para completar?
                  </h3>
                  <p className="text-gray-700">O preenchimento leva em média 30-60 minutos para cada diagnóstico. Recomendamos reservar um tempo adequado para responder com calma e precisão.</p>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <Shield className="h-5 w-5 mr-2 text-green-600" />
                    Os resultados são confidenciais?
                  </h3>
                  <p className="text-gray-700">Sim, todos os dados fornecidos e resultados gerados são totalmente confidenciais e protegidos por nossa política de privacidade.</p>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                    Posso fazer os diagnósticos novamente?
                  </h3>
                  <p className="text-gray-700">Sim, você tem acesso ilimitado aos diagnósticos por 12 meses após a compra. Recomendamos refazer a cada 3-6 meses para acompanhar sua evolução.</p>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <Users className="h-5 w-5 mr-2 text-purple-600" />
                    Como funciona a mentoria?
                  </h3>
                  <p className="text-gray-700">Após receber seu relatório, você pode agendar uma sessão de mentoria com nossos especialistas para discutir os resultados e definir estratégias de implementação.</p>
                </CardContent>
              </Card>

              <Card className="bg-white">
                <CardContent className="p-6">
                  <h3 className="font-bold text-lg mb-3 flex items-center">
                    <Globe className="h-5 w-5 mr-2 text-orange-600" />
                    Funciona para qualquer setor?
                  </h3>
                  <p className="text-gray-700">Sim, nossa metodologia é adaptável a diferentes setores e estágios de negócio, desde startups em fase inicial até empresas em crescimento.</p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      
      <PurchaseAssistant productName="Diagnóstico Empresarial" productType="Serviço de Consultoria" />
      
      <Footer />
    </div>
  );
};

export default Diagnostic;
