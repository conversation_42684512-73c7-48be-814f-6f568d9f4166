
import { useEffect } from "react";
import { <PERSON> } from "react-router-dom";
import { 
  Calendar, 
  Trophy, 
  Calendar as CalendarIcon, 
  Rocket, 
  ArrowRight,
  Presentation,
  Users,
  ChevronRight,
  ChartLine,
  Lightbulb,
  Globe,
  HandCoins
} from "lucide-react";
import Navbar from "@/components/Navbar";
import Footer from "@/components/Footer";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious,
  PaginationEllipsis
} from "@/components/ui/pagination";

// Event data
const upcomingEvents = [
  {
    id: 1,
    title: "ATAC Innovation Summit 2024",
    date: "26/06/2024",
    location: "São Paulo, SP",
    type: "Demo Day",
    startups: 12,
    category: "Multissetorial",
    description: "Apresentação das startups da rodada Seed Capital com foco em soluções SaaS e marketplaces.",
    image: "https://images.unsplash.com/photo-1540304453324-d0e4f048593c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  {
    id: 2,
    title: "Health Innovation Day",
    date: "15/07/2024",
    location: "Rio de Janeiro, RJ",
    type: "Demo Day",
    startups: 8,
    category: "Healthtech",
    description: "Startups especializadas em soluções para o setor de saúde apresentam suas inovações para investidores.",
    image: "https://images.unsplash.com/photo-1576091160550-2173dba999ef?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  {
    id: 3,
    title: "Webinar: Valuation de Startups Early Stage",
    date: "10/07/2024",
    location: "Online",
    type: "Webinar",
    startups: 0,
    category: "Educacional",
    description: "Aprenda métodos eficazes para valorar startups em estágio inicial com especialistas do mercado.",
    image: "https://images.unsplash.com/photo-1616587894289-86480e533129?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  },
  {
    id: 4,
    title: "Fintech Pitch Sessions",
    date: "22/07/2024",
    location: "São Paulo, SP",
    type: "Pitch Day",
    startups: 6,
    category: "Fintech",
    description: "Sessões de pitch de startups fintech que participam do nosso programa de aceleração.",
    image: "https://images.unsplash.com/photo-1559526324-593bc073d938?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80"
  }
];

// Ongoing investment rounds
const ongoingRounds = [
  {
    id: 1,
    title: "Rodada Seed - Agritechs",
    phase: "Tração Inicial",
    startups: 5,
    startDate: "01/06/2024",
    endDate: "15/08/2024",
    category: "Agritech",
    minTicket: "R$ 100 mil"
  },
  {
    id: 2,
    title: "Rodada Corporate - Indústria 4.0",
    phase: "Escala",
    startups: 3,
    startDate: "15/05/2024",
    endDate: "30/07/2024",
    category: "Indústria 4.0",
    minTicket: "R$ 500 mil"
  },
  {
    id: 3,
    title: "Rodada Pré-Seed - Deep Tech",
    phase: "Validação",
    startups: 8,
    startDate: "10/06/2024",
    endDate: "10/09/2024",
    category: "Deep Tech",
    minTicket: "R$ 50 mil"
  }
];

// Completed investment rounds
const completedRounds = [
  {
    id: 1,
    title: "Rodada Seed - SaaS B2B",
    phase: "Tração",
    startups: 6,
    startDate: "10/01/2024",
    endDate: "15/03/2024",
    totalRaised: "R$ 5,4 milhões",
    success: true
  },
  {
    id: 2,
    title: "Rodada Série A - Healthtechs",
    phase: "Expansão",
    startups: 4,
    startDate: "01/02/2024",
    endDate: "30/04/2024",
    totalRaised: "R$ 18 milhões",
    success: true
  },
  {
    id: 3,
    title: "Rodada Pré-Seed - Fintechs",
    phase: "Validação",
    startups: 10,
    startDate: "15/03/2024",
    endDate: "30/05/2024",
    totalRaised: "R$ 3,2 milhões",
    success: true
  },
  {
    id: 4,
    title: "Rodada Corporate - Smart Cities",
    phase: "Escala",
    startups: 3,
    startDate: "01/04/2024",
    endDate: "15/06/2024",
    totalRaised: "R$ 12,5 milhões",
    success: true
  }
];

const OportunidadesInvestidores = () => {
  useEffect(() => {
    window.scrollTo(0, 0);
    document.title = "Oportunidades para Investidores | ATAC Academy";
  }, []);

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="pt-24 pb-20">
        {/* Hero Section */}
        <section className="relative py-16 mb-16 overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-gray-50 to-white z-0"></div>
          <div className="absolute top-0 right-0 w-full h-full opacity-10 z-0">
            <div className="absolute top-0 right-0 w-2/3 h-full bg-gradient-to-bl from-brand/20 to-transparent"></div>
            <div className="absolute bottom-0 left-0 w-2/3 h-2/3 bg-gradient-to-tr from-brand-accent/20 to-transparent"></div>
          </div>
          
          <div className="container mx-auto px-4 relative z-10">
            <div className="max-w-3xl">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-brand-light text-brand font-medium text-sm mb-6">
                <HandCoins className="w-4 h-4 mr-2" /> Área do Investidor
              </div>
              <h1 className="font-montserrat text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                Oportunidades de Investimento em <span className="text-brand-accent">Startups Inovadoras</span>
              </h1>
              <p className="text-xl text-gray-700 mb-8 leading-relaxed">
                Conheça as próximas rodadas de investimento, eventos e Demo Days exclusivos para nossa rede de investidores.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg">
                  Participar de uma rodada
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button size="lg" variant="outline" className="border-brand text-brand hover:bg-brand-light/50">
                  Cadastrar como investidor
                </Button>
              </div>
            </div>
          </div>
        </section>
        
        {/* Upcoming Events Section */}
        <section className="py-16 mb-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-10">
              <div>
                <div className="flex items-center gap-2 text-brand font-medium mb-2">
                  <Calendar className="h-5 w-5" />
                  <span className="uppercase text-sm tracking-wide">Próximos eventos</span>
                </div>
                <h2 className="font-montserrat text-3xl font-bold text-gray-900">
                  Demo Days e Eventos Exclusivos
                </h2>
              </div>
              <Button variant="outline" className="mt-4 md:mt-0 border-brand text-brand hover:bg-brand-light/50">
                Ver Calendário Completo
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {upcomingEvents.map((event) => (
                <Card key={event.id} className="overflow-hidden transition-all hover:shadow-md border-transparent hover:border-brand/30">
                  <div className="relative h-48 bg-gray-100">
                    <div className={`absolute top-4 left-4 ${event.type === 'Demo Day' ? 'bg-brand-accent' : 'bg-brand'} text-white px-3 py-1 rounded-full text-sm font-medium`}>
                      {event.type === 'Demo Day' ? (
                        <div className="flex items-center">
                          <Trophy className="w-4 h-4 mr-1" />
                          {event.type}
                        </div>
                      ) : (
                        <div className="flex items-center">
                          <CalendarIcon className="w-4 h-4 mr-1" />
                          {event.type}
                        </div>
                      )}
                    </div>
                    <img 
                      src={event.image} 
                      alt={event.title} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <CardHeader className="pb-2">
                    <div className="flex justify-between items-center">
                      <CardTitle className="text-xl">{event.title}</CardTitle>
                    </div>
                    <CardDescription>{event.location}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Data</span>
                        <span className="font-medium">{event.date}</span>
                      </div>
                      {event.startups > 0 && (
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Startups</span>
                          <span className="font-medium">{event.startups}</span>
                        </div>
                      )}
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Categoria</span>
                        <span className="font-medium">{event.category}</span>
                      </div>
                      <p className="text-sm text-gray-600 mt-2">{event.description}</p>
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button variant="outline" className="w-full border-brand text-brand hover:bg-brand-light/50">
                      Inscrever-se
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>
        
        {/* Investment Rounds Section */}
        <section className="py-16 mb-16">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-12">
              <div className="inline-flex items-center px-3 py-1 rounded-full bg-brand-light text-brand font-medium text-sm mb-6">
                <Rocket className="w-4 h-4 mr-2" /> Rodadas de Investimento
              </div>
              <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-4">
                Rodadas de Investimento em Andamento
              </h2>
              <p className="text-lg text-gray-700">
                Participe de rodadas de investimento cuidadosamente selecionadas com startups validadas e com potencial de crescimento.
              </p>
            </div>
            
            {/* Explainer Card */}
            <div className="bg-white rounded-xl shadow-md border border-gray-100 p-8 mb-10 max-w-4xl mx-auto">
              <h3 className="font-bold text-xl text-gray-900 mb-4">O que é uma rodada de investimento?</h3>
              <p className="text-gray-700 mb-6">
                A rodada de investimento é uma forma das startups captar recursos para acelerar o crescimento de seus negócios. 
                Neste momento, você, investidor, pode cadastrar uma nova rodada de investimento e as startups poderão visualizar e se cadastrar. 
                Você terá acesso às startups cadastradas e analisar interesse em investir ou enviar um mensagem.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button>
                  Criar nova rodada
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
                <Button variant="outline">
                  Ver como funciona
                </Button>
              </div>
            </div>
            
            <div className="mb-16">
              <h3 className="font-bold text-xl text-gray-900 mb-6 pl-4 border-l-4 border-brand">Rodadas em andamento</h3>
              
              {ongoingRounds.length > 0 ? (
                <div className="space-y-4">
                  {ongoingRounds.map((round) => (
                    <div key={round.id} className="flex flex-col md:flex-row items-start md:items-center justify-between p-6 bg-white rounded-xl shadow-sm border border-gray-100 hover:border-brand/30 transition-colors">
                      <div className="flex items-start gap-4 mb-4 md:mb-0">
                        <div className="w-12 h-12 rounded-full bg-brand-light flex items-center justify-center">
                          <Rocket className="h-6 w-6 text-brand" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-xl text-gray-900">{round.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">
                            <span className="font-medium">Fase:</span> {round.phase} • <span className="font-medium">{round.startups}</span> startups
                          </p>
                          <div className="flex flex-wrap gap-2">
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-brand-light text-brand">
                              {round.category}
                            </span>
                            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                              Ticket mínimo: {round.minTicket}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="flex flex-col md:flex-row items-start md:items-center gap-4">
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Início</div>
                          <div className="font-medium">{round.startDate}</div>
                        </div>
                        <div className="h-6 w-px bg-gray-200 hidden md:block"></div>
                        <div className="text-right">
                          <div className="text-sm text-gray-500">Término</div>
                          <div className="font-medium">{round.endDate}</div>
                        </div>
                        <Button variant="outline" className="md:ml-4 whitespace-nowrap">
                          Ver detalhes
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg p-8 text-center">
                  <p className="text-gray-600">Nenhuma rodada de investimento em andamento</p>
                </div>
              )}
            </div>
            
            <div>
              <h3 className="font-bold text-xl text-gray-900 mb-6 pl-4 border-l-4 border-brand-accent">Rodadas finalizadas</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {completedRounds.map((round) => (
                  <div key={round.id} className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                    <div className="p-6">
                      <h4 className="font-semibold text-lg text-gray-900 mb-2">{round.title}</h4>
                      <div className="flex flex-wrap gap-2 mb-4">
                        <div className="text-sm">
                          <span className="text-gray-500">Fase: </span>
                          <span className="font-medium">{round.phase}</span>
                        </div>
                        <div className="text-sm">
                          <span className="text-gray-500">Startups: </span>
                          <span className="font-medium">{round.startups}</span>
                        </div>
                      </div>
                      <div className="flex justify-between text-sm mb-1">
                        <span className="text-gray-500">Início</span>
                        <span className="font-medium">{round.startDate}</span>
                      </div>
                      <div className="flex justify-between text-sm mb-4">
                        <span className="text-gray-500">Término</span>
                        <span className="font-medium">{round.endDate}</span>
                      </div>
                      <div className="bg-green-50 rounded-lg p-3 mb-4">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-700">Total captado:</span>
                          <span className="font-bold text-green-700">{round.totalRaised}</span>
                        </div>
                      </div>
                      <Button variant="link" className="p-0 h-auto w-full justify-start text-brand hover:text-brand/80">
                        Ver detalhes da rodada
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="mt-8 flex justify-center">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious href="#" />
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#" isActive>1</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">2</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationLink href="#">3</PaginationLink>
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationEllipsis />
                    </PaginationItem>
                    <PaginationItem>
                      <PaginationNext href="#" />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          </div>
        </section>
        
        {/* Benefits Section */}
        <section className="py-16 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-3xl mx-auto text-center mb-12">
              <h2 className="font-montserrat text-3xl font-bold text-gray-900 mb-4">
                Por que investir através da nossa plataforma?
              </h2>
              <p className="text-lg text-gray-700">
                Oferecemos uma experiência completa e segura para investidores que buscam oportunidades em startups inovadoras.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                <div className="w-14 h-14 rounded-full bg-brand-light flex items-center justify-center mb-6">
                  <Presentation className="h-7 w-7 text-brand" />
                </div>
                <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-3">
                  Demo Days Exclusivos
                </h3>
                <p className="text-gray-700">
                  Participe de eventos exclusivos onde as startups mais promissoras apresentam suas soluções e modelos de negócio.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                <div className="w-14 h-14 rounded-full bg-brand-light flex items-center justify-center mb-6">
                  <ChartLine className="h-7 w-7 text-brand" />
                </div>
                <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-3">
                  Due Diligence Completa
                </h3>
                <p className="text-gray-700">
                  Todas as startups passam por um rigoroso processo de análise antes de serem apresentadas aos investidores.
                </p>
              </div>
              
              <div className="bg-white p-8 rounded-xl shadow-sm border border-gray-100">
                <div className="w-14 h-14 rounded-full bg-brand-light flex items-center justify-center mb-6">
                  <Users className="h-7 w-7 text-brand" />
                </div>
                <h3 className="font-montserrat text-xl font-bold text-gray-900 mb-3">
                  Networking Estratégico
                </h3>
                <p className="text-gray-700">
                  Conecte-se com outros investidores, fundos e corporações para coinvestimento e troca de experiências.
                </p>
              </div>
            </div>
          </div>
        </section>
        
        {/* CTA Section */}
        <section className="py-20 bg-gradient-to-r from-brand to-brand-accent text-white">
          <div className="container mx-auto px-4 text-center">
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold mb-6">
              Pronto para investir em startups inovadoras?
            </h2>
            <p className="text-xl mb-10 max-w-2xl mx-auto text-white/90">
              Cadastre-se como investidor e tenha acesso a todas as oportunidades, eventos e Demo Days exclusivos.
            </p>
            
            <div className="flex flex-wrap justify-center gap-4">
              <Button size="lg" className="bg-white text-brand hover:bg-gray-100">
                Cadastrar como investidor
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
              <Button size="lg" variant="outline" className="border-white text-white hover:bg-white/10">
                Falar com um especialista
              </Button>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </div>
  );
};

export default OportunidadesInvestidores;
