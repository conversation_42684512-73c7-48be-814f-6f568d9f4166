
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Clock, BookOpen, Star, Rocket, Globe, Share2, Tag } from "lucide-react";
import { <PERSON> } from "react-router-dom";

const featuredPrograms = [
  {
    id: "ai-na-pratica",
    title: "AI na Prática",
    description: "Aprenda a implementar soluções práticas de IA em seus projetos e negócios com foco em resultados tangíveis",
    image: "/lovable-uploads/ebdd26ce-4835-4e4c-91b6-abbfad51eac7.png",
    duration: "6 horas",
    lessons: 6,
    workshops: 0,
    rating: 4.7,
    price: "R$ 2.500",
    promotionPrice: "R$ 1.750",
    hasPromotion: true,
    instructor: "<PERSON>",
    category: "Startup-se",
    type: "Online",
    url: "/curso/ai-na-pratica",
    showPrice: true
  },
  {
    id: "startup-revolution",
    title: "Startup Revolution",
    description: "Programa para empreendedores com ideias inovadoras que precisam de direcionamento para decolar",
    image: "/lovable-uploads/765a19e3-9747-45ff-b27b-4ae988011be8.png",
    duration: "6 semanas",
    lessons: 12,
    workshops: 6,
    rating: 4.7,
    price: "R$ 2.997",
    instructor: "Equipe ATAC",
    category: "Acelere seu Negócio",
    type: "Online e ao vivo",
    url: "/curso/startup-revolution",
    showPrice: true
  },
  {
    id: "startup-bootcamp",
    title: "Startup Bootcamp",
    description: "Estação de base da ideia ao modelo e à estratégia do negócio para startups em fase inicial",
    image: "/lovable-uploads/d4c2a54f-d96c-4b89-a537-d20f73a5cd40.png",
    duration: "4 semanas",
    lessons: 8,
    workshops: 4,
    rating: 4.8,
    price: "R$ 1.997",
    instructor: "Equipe ATAC",
    category: "Acelere seu Negócio",
    type: "Online e ao vivo",
    url: "/curso/startup-bootcamp",
    showPrice: true
  }
];

const Courses = () => {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row md:items-end md:justify-between mb-16">
          <div>
            <h2 className="font-montserrat text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Programas de Aceleração
            </h2>
            <p className="text-lg text-gray-700 max-w-2xl">
              Desenvolvidos para empreendedores e executivos que buscam escalar seus negócios
            </p>
          </div>
          <Link to="/cursos" className="mt-6 md:mt-0">
            <Button variant="link" className="text-brand hover:text-brand-secondary text-lg">
              Ver todos os programas
            </Button>
          </Link>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {featuredPrograms.map((course) => (
            <Card key={course.id} className="overflow-hidden border border-gray-100 shadow-md hover:shadow-lg transition-all duration-300 group hover:-translate-y-1">
              <div className="relative h-52 overflow-hidden">
                <img 
                  src={course.image} 
                  alt={course.title}
                  className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105"
                />
                <div className="absolute top-4 right-4 bg-brand-accent text-white text-sm font-medium py-1 px-3 rounded-full">
                  {course.category}
                </div>
                <div className="absolute bottom-4 left-4 bg-white/90 text-brand text-sm font-medium py-1 px-3 rounded-full">
                  {course.type}
                </div>
                {course.hasPromotion && (
                  <div className="absolute top-4 left-4 bg-red-500 text-white text-sm font-medium py-1 px-3 rounded-full flex items-center">
                    <Tag className="h-3 w-3 mr-1" />
                    Promoção
                  </div>
                )}
              </div>
              <CardHeader className="p-6 pb-2">
                <h3 className="font-montserrat text-xl font-semibold text-gray-900 group-hover:text-brand transition-colors">
                  {course.title}
                </h3>
              </CardHeader>
              <CardContent className="p-6 pt-2 pb-4">
                <p className="text-gray-700 mb-4 line-clamp-2">{course.description}</p>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <Clock className="h-4 w-4 text-brand-accent mr-2" />
                    <span className="text-sm">{course.duration}</span>
                  </div>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 fill-yellow-500 mr-2" />
                    <span className="text-sm">{course.rating}</span>
                  </div>
                  {course.lessons > 0 && (
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 text-brand mr-2" />
                      <span className="text-sm">{course.lessons} aulas</span>
                    </div>
                  )}
                  <div className="flex items-center">
                    <Globe className="h-4 w-4 text-brand-accent mr-2" />
                    <span className="text-sm">{course.workshops} workshops</span>
                  </div>
                </div>
                {(course.category !== "Open Innovation" && course.showPrice !== false) && (
                  <div className="flex flex-col items-end mt-4">
                    {course.hasPromotion ? (
                      <>
                        <span className="text-gray-500 line-through text-sm">{course.price}</span>
                        <span className="text-red-500 font-bold text-xl">{course.promotionPrice}</span>
                      </>
                    ) : (
                      <span className="text-brand font-bold text-xl">{course.price}</span>
                    )}
                  </div>
                )}
              </CardContent>
              <CardFooter className="p-6 pt-0">
                <Link to={course.url} className="w-full">
                  <Button className="w-full bg-brand hover:bg-brand-secondary">
                    Saiba Mais
                  </Button>
                </Link>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </section>
  );
};

export default Courses;
